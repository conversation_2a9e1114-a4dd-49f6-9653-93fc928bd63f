-- Merging decision tree log ---
application
INJECTED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:12:5-42:19
INJECTED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\debug\AndroidManifest.xml
MERGED from [:firebase_database] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_database\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_database] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_database\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:location] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-16:19
MERGED from [:location] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-16:19
MERGED from [:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-20:19
MERGED from [:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-20:19
MERGED from [:printing] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-17:19
MERGED from [:printing] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-17:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-12:19
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-analytics:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d6066d1f58d1da1ec191a793d19486a\transformed\jetified-firebase-analytics-21.6.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d6066d1f58d1da1ec191a793d19486a\transformed\jetified-firebase-analytics-21.6.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\43bd576f6b047dc34762866e59a2bdc9\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\43bd576f6b047dc34762866e59a2bdc9\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:22:5-39:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d8011ecb2b4abbe6dd1ee65c0a16a62\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d8011ecb2b4abbe6dd1ee65c0a16a62\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\785a97bcaa4fda868c4e5ed63fbd957d\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\785a97bcaa4fda868c4e5ed63fbd957d\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f4e70bc3025616eb9889b997a170b83f\transformed\jetified-play-services-measurement-sdk-21.6.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f4e70bc3025616eb9889b997a170b83f\transformed\jetified-play-services-measurement-sdk-21.6.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e62ef97ed4cfc0b07b064df5b41704f\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e62ef97ed4cfc0b07b064df5b41704f\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad49122f8961802a5c9215f909e107c\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad49122f8961802a5c9215f909e107c\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c4729c540afc85a20200e7f28f92586\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c4729c540afc85a20200e7f28f92586\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee45d102573a3c216330482f616bbf9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee45d102573a3c216330482f616bbf9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\168bd0f15ab005f7a0a1f8813311f97f\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\168bd0f15ab005f7a0a1f8813311f97f\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc7de8790058e921ba0aca1c8afd3a30\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc7de8790058e921ba0aca1c8afd3a30\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\465fa5597554e8ef5eeea5f4462d0db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\465fa5597554e8ef5eeea5f4462d0db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\b8db4b3108bb0d810b720b08b0e8e9d6\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\b8db4b3108bb0d810b720b08b0e8e9d6\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\03daaf70cf2fe1394626508bdf78e6c8\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\03daaf70cf2fe1394626508bdf78e6c8\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7afcdce26cc9a041f51873508c33b120\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7afcdce26cc9a041f51873508c33b120\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f8e7503b9949bea4b05d8cc720b88643\transformed\jetified-play-services-measurement-base-21.6.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f8e7503b9949bea4b05d8cc720b88643\transformed\jetified-play-services-measurement-base-21.6.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6db2094c2514e2aa599ce5740e5202e6\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6db2094c2514e2aa599ce5740e5202e6\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0297ec01e5faeda16541291842a915c\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0297ec01e5faeda16541291842a915c\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9875b94023218862e491225b594e2ab9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9875b94023218862e491225b594e2ab9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\007b9eb2fc10e998ee1a8814d8bc5a84\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\007b9eb2fc10e998ee1a8814d8bc5a84\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml
manifest
ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:2:1-54:12
MERGED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:2:1-54:12
INJECTED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:firebase_database] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_database\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [:location] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-18:12
MERGED from [:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-22:12
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:printing] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-19:12
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.firebase:firebase-analytics:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d6066d1f58d1da1ec191a793d19486a\transformed\jetified-firebase-analytics-21.6.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\43bd576f6b047dc34762866e59a2bdc9\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:15:1-41:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3efe6e37a4594f3502d33b12b5c5b9a7\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b8db302278a2348d29ffa46618bac34\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\77bde75c00e32956beb51ba5a0a1bb44\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\c7a0a157a794b254ce184add68158ec7\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a602e53b9385aa950d2ba64643d4c8e2\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\246854d1b5a7344582217b4650693179\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d96c70204dc2769a97012f8fcc0d125\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d8011ecb2b4abbe6dd1ee65c0a16a62\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\08e559afd90a3957aa515e701ccd3d1b\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\785a97bcaa4fda868c4e5ed63fbd957d\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f4e70bc3025616eb9889b997a170b83f\transformed\jetified-play-services-measurement-sdk-21.6.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e62ef97ed4cfc0b07b064df5b41704f\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad49122f8961802a5c9215f909e107c\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\4b2e3834408718c0f30c30560f1d06e2\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c4729c540afc85a20200e7f28f92586\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d2b031c4c6408d8880dbb84b445089e2\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\254a79ee22263bc14d4aeeeea8a6321b\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\add6c8fde871611cbf3ad04ce9b57240\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\34968cb80ddda30a34e9ab93db8b7bec\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\35d4e7da13ad827a8e5e4ca5f332c624\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee45d102573a3c216330482f616bbf9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\168bd0f15ab005f7a0a1f8813311f97f\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbad37007c911be209208051dd54896c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d604af742c330fbc70c43d37ad0948e1\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7bd8a891829467a45a58e5eca482660f\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a2db7434f706a2bf030f3d686629ac8\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3ca3ae02701177aed85c52f3f6e6058\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ec7222879f17b1cb2f665c54da106f8\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\aebf4753d75e1318e96cf46409104cd0\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4eb6cf9398dff74e21a0405f34dc3ff\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8f6f0f9a9f80658012d1a7b42c784ae\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad43c39f90feae093353608e7fe267ea\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b773f8bddc4000b2a11e6e734b86f0a7\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc7de8790058e921ba0aca1c8afd3a30\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\465fa5597554e8ef5eeea5f4462d0db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\0cbcd0c96a9408c0d58002e76ced53b9\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\bab9f331524eb5597fa17caa807451fb\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\dd71c54ab6327d87368ec01359e56104\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f0a658ce8c7e178d4202a7d9d0feeda3\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\c4fa7f3f5daba117e6f36b6c6b271856\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\51c7baa3d5f9afc8294469e6d8181028\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\b8db4b3108bb0d810b720b08b0e8e9d6\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\03daaf70cf2fe1394626508bdf78e6c8\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7afcdce26cc9a041f51873508c33b120\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9cd226cc620395442539c9b0389bf35e\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f8e7503b9949bea4b05d8cc720b88643\transformed\jetified-play-services-measurement-base-21.6.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6db2094c2514e2aa599ce5740e5202e6\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0297ec01e5faeda16541291842a915c\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e0a06cca00889b561d1f83f31c35aee\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\cabed1d871df784a66685d9c83687a94\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9fb468f053940ada77c91bfc8ccecb00\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\915a190d7f622a026d8e9b56474a8fa9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee91702a881cbe3f9d74b6d82001eca1\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0d9df7c14103e2fac2e8000889450f18\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8ce8b14533db0e1230778e43a127e5e\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\d770519b97da5d5eb49587f75ea6c2ed\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\24f416bafa069f91d3cefaac5f9067a2\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d5ce34f772b3932c3a4d23c0e1faf55\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9875b94023218862e491225b594e2ab9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\87547deb9abe9b3dd6c7e0fbd5118d7b\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a71df5f19183fb19f8c256121720b27\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e251ddfc75f5aa68cf9b292bd2f93c1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf2567e5e7dd195a253738e0f605e8dc\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\007b9eb2fc10e998ee1a8814d8bc5a84\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\16531e2f125b05c037359b655832a6f7\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f39d9f3932f30e6bdce5e289828ce383\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc0b2fcd4f9d9bd92c3798f7a4165ab0\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\481abf15ed1cd8ae545486cfce185271\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a66baf00d1a80bfdb356afa763d4a8d9\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\87bbe4a38a15029718b500e2f218c601\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.multidex:multidex:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6cde73798b831d1c9da6fd901b6e9e7a\transformed\multidex-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\462079370238a1ce92afa7ce311beb36\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:3:5-66
MERGED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:3:5-66
MERGED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:3:5-66
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee45d102573a3c216330482f616bbf9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee45d102573a3c216330482f616bbf9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9cd226cc620395442539c9b0389bf35e\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9cd226cc620395442539c9b0389bf35e\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:23:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:3:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:4:5-78
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee45d102573a3c216330482f616bbf9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee45d102573a3c216330482f616bbf9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9cd226cc620395442539c9b0389bf35e\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9cd226cc620395442539c9b0389bf35e\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:4:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee45d102573a3c216330482f616bbf9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee45d102573a3c216330482f616bbf9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9cd226cc620395442539c9b0389bf35e\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9cd226cc620395442539c9b0389bf35e\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:25:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:5:22-65
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:6:5-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:6:22-63
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:7:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:7:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:8:5-78
MERGED from [:location] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-79
MERGED from [:location] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:8:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:9:5-80
MERGED from [:location] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-81
MERGED from [:location] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:10:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:10:22-74
queries
ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:48:5-53:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:49:9-52:18
action#android.intent.action.PROCESS_TEXT
ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:50:13-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:50:21-70
data
ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:51:13-50
	android:mimeType
		ADDED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:51:19-48
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\debug\AndroidManifest.xml
MERGED from [:firebase_database] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_database\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_database] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_database\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:location] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:location] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:printing] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:printing] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-analytics:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d6066d1f58d1da1ec191a793d19486a\transformed\jetified-firebase-analytics-21.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d6066d1f58d1da1ec191a793d19486a\transformed\jetified-firebase-analytics-21.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\43bd576f6b047dc34762866e59a2bdc9\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\43bd576f6b047dc34762866e59a2bdc9\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:19:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3efe6e37a4594f3502d33b12b5c5b9a7\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3efe6e37a4594f3502d33b12b5c5b9a7\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b8db302278a2348d29ffa46618bac34\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b8db302278a2348d29ffa46618bac34\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\77bde75c00e32956beb51ba5a0a1bb44\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\77bde75c00e32956beb51ba5a0a1bb44\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\c7a0a157a794b254ce184add68158ec7\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\c7a0a157a794b254ce184add68158ec7\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a602e53b9385aa950d2ba64643d4c8e2\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a602e53b9385aa950d2ba64643d4c8e2\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\246854d1b5a7344582217b4650693179\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\246854d1b5a7344582217b4650693179\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d96c70204dc2769a97012f8fcc0d125\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d96c70204dc2769a97012f8fcc0d125\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d8011ecb2b4abbe6dd1ee65c0a16a62\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d8011ecb2b4abbe6dd1ee65c0a16a62\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\08e559afd90a3957aa515e701ccd3d1b\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\08e559afd90a3957aa515e701ccd3d1b\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\785a97bcaa4fda868c4e5ed63fbd957d\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\785a97bcaa4fda868c4e5ed63fbd957d\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f4e70bc3025616eb9889b997a170b83f\transformed\jetified-play-services-measurement-sdk-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f4e70bc3025616eb9889b997a170b83f\transformed\jetified-play-services-measurement-sdk-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e62ef97ed4cfc0b07b064df5b41704f\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e62ef97ed4cfc0b07b064df5b41704f\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad49122f8961802a5c9215f909e107c\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ad49122f8961802a5c9215f909e107c\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\4b2e3834408718c0f30c30560f1d06e2\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\4b2e3834408718c0f30c30560f1d06e2\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c4729c540afc85a20200e7f28f92586\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c4729c540afc85a20200e7f28f92586\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d2b031c4c6408d8880dbb84b445089e2\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d2b031c4c6408d8880dbb84b445089e2\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\254a79ee22263bc14d4aeeeea8a6321b\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\254a79ee22263bc14d4aeeeea8a6321b\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\add6c8fde871611cbf3ad04ce9b57240\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\add6c8fde871611cbf3ad04ce9b57240\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\34968cb80ddda30a34e9ab93db8b7bec\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\34968cb80ddda30a34e9ab93db8b7bec\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\35d4e7da13ad827a8e5e4ca5f332c624\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\35d4e7da13ad827a8e5e4ca5f332c624\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee45d102573a3c216330482f616bbf9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee45d102573a3c216330482f616bbf9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\168bd0f15ab005f7a0a1f8813311f97f\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\168bd0f15ab005f7a0a1f8813311f97f\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbad37007c911be209208051dd54896c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbad37007c911be209208051dd54896c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d604af742c330fbc70c43d37ad0948e1\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d604af742c330fbc70c43d37ad0948e1\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7bd8a891829467a45a58e5eca482660f\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7bd8a891829467a45a58e5eca482660f\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a2db7434f706a2bf030f3d686629ac8\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a2db7434f706a2bf030f3d686629ac8\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3ca3ae02701177aed85c52f3f6e6058\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3ca3ae02701177aed85c52f3f6e6058\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ec7222879f17b1cb2f665c54da106f8\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ec7222879f17b1cb2f665c54da106f8\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\aebf4753d75e1318e96cf46409104cd0\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\aebf4753d75e1318e96cf46409104cd0\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4eb6cf9398dff74e21a0405f34dc3ff\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4eb6cf9398dff74e21a0405f34dc3ff\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8f6f0f9a9f80658012d1a7b42c784ae\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8f6f0f9a9f80658012d1a7b42c784ae\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad43c39f90feae093353608e7fe267ea\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad43c39f90feae093353608e7fe267ea\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b773f8bddc4000b2a11e6e734b86f0a7\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b773f8bddc4000b2a11e6e734b86f0a7\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc7de8790058e921ba0aca1c8afd3a30\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc7de8790058e921ba0aca1c8afd3a30\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\465fa5597554e8ef5eeea5f4462d0db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\465fa5597554e8ef5eeea5f4462d0db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\0cbcd0c96a9408c0d58002e76ced53b9\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\0cbcd0c96a9408c0d58002e76ced53b9\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\bab9f331524eb5597fa17caa807451fb\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\bab9f331524eb5597fa17caa807451fb\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\dd71c54ab6327d87368ec01359e56104\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\dd71c54ab6327d87368ec01359e56104\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f0a658ce8c7e178d4202a7d9d0feeda3\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f0a658ce8c7e178d4202a7d9d0feeda3\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\c4fa7f3f5daba117e6f36b6c6b271856\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\c4fa7f3f5daba117e6f36b6c6b271856\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\51c7baa3d5f9afc8294469e6d8181028\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\51c7baa3d5f9afc8294469e6d8181028\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\b8db4b3108bb0d810b720b08b0e8e9d6\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\b8db4b3108bb0d810b720b08b0e8e9d6\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\03daaf70cf2fe1394626508bdf78e6c8\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\03daaf70cf2fe1394626508bdf78e6c8\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7afcdce26cc9a041f51873508c33b120\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7afcdce26cc9a041f51873508c33b120\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9cd226cc620395442539c9b0389bf35e\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9cd226cc620395442539c9b0389bf35e\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f8e7503b9949bea4b05d8cc720b88643\transformed\jetified-play-services-measurement-base-21.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f8e7503b9949bea4b05d8cc720b88643\transformed\jetified-play-services-measurement-base-21.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6db2094c2514e2aa599ce5740e5202e6\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6db2094c2514e2aa599ce5740e5202e6\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0297ec01e5faeda16541291842a915c\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0297ec01e5faeda16541291842a915c\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e0a06cca00889b561d1f83f31c35aee\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e0a06cca00889b561d1f83f31c35aee\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\cabed1d871df784a66685d9c83687a94\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\cabed1d871df784a66685d9c83687a94\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9fb468f053940ada77c91bfc8ccecb00\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9fb468f053940ada77c91bfc8ccecb00\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\915a190d7f622a026d8e9b56474a8fa9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\915a190d7f622a026d8e9b56474a8fa9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee91702a881cbe3f9d74b6d82001eca1\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee91702a881cbe3f9d74b6d82001eca1\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0d9df7c14103e2fac2e8000889450f18\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0d9df7c14103e2fac2e8000889450f18\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8ce8b14533db0e1230778e43a127e5e\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8ce8b14533db0e1230778e43a127e5e\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\d770519b97da5d5eb49587f75ea6c2ed\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\d770519b97da5d5eb49587f75ea6c2ed\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\24f416bafa069f91d3cefaac5f9067a2\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\24f416bafa069f91d3cefaac5f9067a2\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d5ce34f772b3932c3a4d23c0e1faf55\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d5ce34f772b3932c3a4d23c0e1faf55\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9875b94023218862e491225b594e2ab9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9875b94023218862e491225b594e2ab9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\87547deb9abe9b3dd6c7e0fbd5118d7b\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\87547deb9abe9b3dd6c7e0fbd5118d7b\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a71df5f19183fb19f8c256121720b27\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a71df5f19183fb19f8c256121720b27\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e251ddfc75f5aa68cf9b292bd2f93c1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e251ddfc75f5aa68cf9b292bd2f93c1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf2567e5e7dd195a253738e0f605e8dc\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf2567e5e7dd195a253738e0f605e8dc\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\007b9eb2fc10e998ee1a8814d8bc5a84\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\007b9eb2fc10e998ee1a8814d8bc5a84\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\16531e2f125b05c037359b655832a6f7\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\16531e2f125b05c037359b655832a6f7\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f39d9f3932f30e6bdce5e289828ce383\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f39d9f3932f30e6bdce5e289828ce383\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc0b2fcd4f9d9bd92c3798f7a4165ab0\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc0b2fcd4f9d9bd92c3798f7a4165ab0\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\481abf15ed1cd8ae545486cfce185271\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\481abf15ed1cd8ae545486cfce185271\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a66baf00d1a80bfdb356afa763d4a8d9\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a66baf00d1a80bfdb356afa763d4a8d9\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\87bbe4a38a15029718b500e2f218c601\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\87bbe4a38a15029718b500e2f218c601\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.multidex:multidex:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6cde73798b831d1c9da6fd901b6e9e7a\transformed\multidex-2.0.0\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6cde73798b831d1c9da6fd901b6e9e7a\transformed\multidex-2.0.0\AndroidManifest.xml:20:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\462079370238a1ce92afa7ce311beb36\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\462079370238a1ce92afa7ce311beb36\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\debug\AndroidManifest.xml
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_database] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_database\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\43bd576f6b047dc34762866e59a2bdc9\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\43bd576f6b047dc34762866e59a2bdc9\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:28:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_database] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_database\intermediates\merged_manifest\debug\AndroidManifest.xml:8:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.database.FlutterFirebaseAppRegistrar
ADDED from [:firebase_database] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_database\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_database] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_database\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_database] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_database\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-127
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
service#com.lyokone.location.FlutterLocationService
ADDED from [:location] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:11:9-15:56
	android:enabled
		ADDED from [:location] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-35
	android:exported
		ADDED from [:location] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-37
	android:foregroundServiceType
		ADDED from [:location] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-53
	android:name
		ADDED from [:location] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-71
provider#com.crazecoder.openfile.FileProvider
ADDED from [:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-19:20
	android:requestLegacyExternalStorage
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-56
	android:grantUriPermissions
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-88
	android:exported
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
	tools:replace
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-48
	android:name
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-64
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-18:53
	android:resource
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:18:17-50
	android:name
		ADDED from [:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:17:17-67
provider#net.nfet.flutter.printing.PrintFileProvider
ADDED from [:printing] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:printing] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:printing] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-68
	android:exported
		ADDED from [:printing] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:printing] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-71
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
meta-data#com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar
ADDED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:30:17-120
meta-data#com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar
ADDED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:33:17-109
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee45d102573a3c216330482f616bbf9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee45d102573a3c216330482f616bbf9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7afcdce26cc9a041f51873508c33b120\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7afcdce26cc9a041f51873508c33b120\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9cd226cc620395442539c9b0389bf35e\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9cd226cc620395442539c9b0389bf35e\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9cd226cc620395442539c9b0389bf35e\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9cd226cc620395442539c9b0389bf35e\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9cd226cc620395442539c9b0389bf35e\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9cd226cc620395442539c9b0389bf35e\transformed\jetified-play-services-measurement-sdk-api-21.6.1\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:22-79
property
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:38:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\43bd576f6b047dc34762866e59a2bdc9\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\43bd576f6b047dc34762866e59a2bdc9\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\43bd576f6b047dc34762866e59a2bdc9\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d8011ecb2b4abbe6dd1ee65c0a16a62\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d8011ecb2b4abbe6dd1ee65c0a16a62\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d8011ecb2b4abbe6dd1ee65c0a16a62\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d8011ecb2b4abbe6dd1ee65c0a16a62\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d8011ecb2b4abbe6dd1ee65c0a16a62\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d8011ecb2b4abbe6dd1ee65c0a16a62\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee45d102573a3c216330482f616bbf9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee45d102573a3c216330482f616bbf9\transformed\jetified-play-services-measurement-impl-21.6.1\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:40:13-87
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c4729c540afc85a20200e7f28f92586\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c4729c540afc85a20200e7f28f92586\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c4729c540afc85a20200e7f28f92586\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c4729c540afc85a20200e7f28f92586\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc7de8790058e921ba0aca1c8afd3a30\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\465fa5597554e8ef5eeea5f4462d0db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\465fa5597554e8ef5eeea5f4462d0db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9875b94023218862e491225b594e2ab9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9875b94023218862e491225b594e2ab9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc7de8790058e921ba0aca1c8afd3a30\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc7de8790058e921ba0aca1c8afd3a30\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc7de8790058e921ba0aca1c8afd3a30\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc7de8790058e921ba0aca1c8afd3a30\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc7de8790058e921ba0aca1c8afd3a30\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc7de8790058e921ba0aca1c8afd3a30\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc7de8790058e921ba0aca1c8afd3a30\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\465fa5597554e8ef5eeea5f4462d0db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\465fa5597554e8ef5eeea5f4462d0db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\465fa5597554e8ef5eeea5f4462d0db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\b8db4b3108bb0d810b720b08b0e8e9d6\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\b8db4b3108bb0d810b720b08b0e8e9d6\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\b8db4b3108bb0d810b720b08b0e8e9d6\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0297ec01e5faeda16541291842a915c\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0297ec01e5faeda16541291842a915c\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0297ec01e5faeda16541291842a915c\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.farmflow.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.farmflow.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
