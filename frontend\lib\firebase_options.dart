import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBk-LeGSskGSjZa4pVYPr2kS759Hp86Cp4',
    appId: '1:486523327700:web:18fdeeeca6d47af8041741',
    messagingSenderId: '486523327700',
    projectId: 'farmflow-a2716',
    authDomain: 'farmflow-a2716.firebaseapp.com',
    databaseURL: 'https://farmflow-a2716-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'farmflow-a2716.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBk-LeGSskGSjZa4pVYPr2kS759Hp86Cp4',
    appId: '1:486523327700:android:18fdeeeca6d47af8041741',
    messagingSenderId: '486523327700',
    projectId: 'farmflow-a2716',
    databaseURL: 'https://farmflow-a2716-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'farmflow-a2716.firebasestorage.app',
    androidClientId: '486523327700-8h9j2k3l4m5n6o7p8q9r0s1t2u3v4w5x.apps.googleusercontent.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBk-LeGSskGSjZa4pVYPr2kS759Hp86Cp4',
    appId: '1:486523327700:ios:18fdeeeca6d47af8041741',
    messagingSenderId: '486523327700',
    projectId: 'farmflow-a2716',
    databaseURL: 'https://farmflow-a2716-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'farmflow-a2716.firebasestorage.app',
    iosClientId: '486523327700-i1o2s3c4l5i6e7n8t9i0d1a2b3c4d5e6.apps.googleusercontent.com',
    iosBundleId: 'com.example.farmflow',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBk-LeGSskGSjZa4pVYPr2kS759Hp86Cp4',
    appId: '1:486523327700:macos:18fdeeeca6d47af8041741',
    messagingSenderId: '486523327700',
    projectId: 'farmflow-a2716',
    databaseURL: 'https://farmflow-a2716-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'farmflow-a2716.firebasestorage.app',
    iosClientId: '486523327700-m1a2c3o4s5c6l7i8e9n0t1i2d3a4b5c6.apps.googleusercontent.com',
    iosBundleId: 'com.example.farmflow',
  );
} 