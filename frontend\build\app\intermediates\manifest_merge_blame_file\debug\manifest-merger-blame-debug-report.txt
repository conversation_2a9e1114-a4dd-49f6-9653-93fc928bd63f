1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.farmflow"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:3:5-66
15-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:3:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:4:5-78
16-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:4:22-76
17    <uses-permission android:name="android.permission.WAKE_LOCK" />
17-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:5:5-67
17-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:5:22-65
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:6:5-65
18-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:6:22-63
19    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
19-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:7:5-80
19-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:7:22-78
20    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
20-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:8:5-78
20-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:8:22-76
21    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
21-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:9:5-80
21-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:9:22-78
22    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
22-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:10:5-76
22-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:10:22-74
23    <!--
24 Required to query activities that can process text, see:
25         https://developer.android.com/training/package-visibility and
26         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
27
28         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
29    -->
30    <queries>
30-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:48:5-53:15
31        <intent>
31-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:49:9-52:18
32            <action android:name="android.intent.action.PROCESS_TEXT" />
32-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:50:13-72
32-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:50:21-70
33
34            <data android:mimeType="text/plain" />
34-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:51:13-50
34-->C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\android\app\src\main\AndroidManifest.xml:51:19-48
35        </intent>
36    </queries>
37
38    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
38-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:5-79
38-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:22-76
39    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
39-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:5-88
39-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:22-85
40    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
40-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:5-82
40-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:22-79
41    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
41-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:5-110
41-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:22-107
42
43    <permission
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
44        android:name="com.example.farmflow.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.example.farmflow.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
48
49    <application
50        android:name="android.app.Application"
51        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\306dcb4061937b668c92c78e7d493e36\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
52        android:debuggable="true"
53        android:extractNativeLibs="true"
54        android:icon="@mipmap/ic_launcher"
55        android:label="FarmFlow" >
56        <activity
57            android:name="com.example.farmflow.MainActivity"
58            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
59            android:exported="true"
60            android:hardwareAccelerated="true"
61            android:launchMode="singleTop"
62            android:theme="@style/LaunchTheme"
63            android:windowSoftInputMode="adjustResize" >
64
65            <!--
66                 Specifies an Android theme to apply to this Activity as soon as
67                 the Android process has started. This theme is visible to the user
68                 while the Flutter UI initializes. After that, this theme continues
69                 to determine the Window background behind the Flutter UI.
70            -->
71            <meta-data
72                android:name="io.flutter.embedding.android.NormalTheme"
73                android:resource="@style/NormalTheme" />
74
75            <intent-filter>
76                <action android:name="android.intent.action.MAIN" />
77
78                <category android:name="android.intent.category.LAUNCHER" />
79            </intent-filter>
80        </activity>
81        <!--
82             Don't delete the meta-data below.
83             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
84        -->
85        <meta-data
86            android:name="flutterEmbedding"
87            android:value="2" />
88
89        <service
89-->[:firebase_database] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_database\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
90            android:name="com.google.firebase.components.ComponentDiscoveryService"
90-->[:firebase_database] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_database\intermediates\merged_manifest\debug\AndroidManifest.xml:8:18-89
91            android:directBootAware="true"
91-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
92            android:exported="false" >
92-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:28:13-37
93            <meta-data
93-->[:firebase_database] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_database\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
94                android:name="com.google.firebase.components:io.flutter.plugins.firebase.database.FlutterFirebaseAppRegistrar"
94-->[:firebase_database] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_database\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-127
95                android:value="com.google.firebase.components.ComponentRegistrar" />
95-->[:firebase_database] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_database\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
96            <meta-data
96-->[:firebase_core] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
97                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
97-->[:firebase_core] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
98                android:value="com.google.firebase.components.ComponentRegistrar" />
98-->[:firebase_core] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
99            <meta-data
99-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:29:13-31:85
100                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
100-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:30:17-120
101                android:value="com.google.firebase.components.ComponentRegistrar" />
101-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:31:17-82
102            <meta-data
102-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:32:13-34:85
103                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
103-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:33:17-109
104                android:value="com.google.firebase.components.ComponentRegistrar" />
104-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d76296078f3c27f0400d3f42ede9a3a6\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:34:17-82
105            <meta-data
105-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:37:13-39:85
106                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
106-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:38:17-139
107                android:value="com.google.firebase.components.ComponentRegistrar" />
107-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:39:17-82
108            <meta-data
108-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
109                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
109-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
110                android:value="com.google.firebase.components.ComponentRegistrar" />
110-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
111            <meta-data
111-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
112                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
112-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
113                android:value="com.google.firebase.components.ComponentRegistrar" />
113-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\80893a042c5597cf118f06efcf6711ba\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
114            <meta-data
114-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\43bd576f6b047dc34762866e59a2bdc9\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
115                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
115-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\43bd576f6b047dc34762866e59a2bdc9\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
116                android:value="com.google.firebase.components.ComponentRegistrar" />
116-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\43bd576f6b047dc34762866e59a2bdc9\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
117            <meta-data
117-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
118                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
118-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
119                android:value="com.google.firebase.components.ComponentRegistrar" />
119-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
120        </service>
121        <service
121-->[:location] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:11:9-15:56
122            android:name="com.lyokone.location.FlutterLocationService"
122-->[:location] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-71
123            android:enabled="true"
123-->[:location] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-35
124            android:exported="false"
124-->[:location] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-37
125            android:foregroundServiceType="location" />
125-->[:location] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-53
126
127        <provider
127-->[:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-19:20
128            android:name="com.crazecoder.openfile.FileProvider"
128-->[:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-64
129            android:authorities="com.example.farmflow.fileProvider.com.crazecoder.openfile"
129-->[:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-88
130            android:exported="false"
130-->[:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
131            android:grantUriPermissions="true"
131-->[:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
132            android:requestLegacyExternalStorage="true" >
132-->[:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-56
133            <meta-data
133-->[:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-18:53
134                android:name="android.support.FILE_PROVIDER_PATHS"
134-->[:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:17:17-67
135                android:resource="@xml/filepaths" />
135-->[:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:18:17-50
136        </provider>
137        <provider
137-->[:printing] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-16:20
138            android:name="net.nfet.flutter.printing.PrintFileProvider"
138-->[:printing] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-71
139            android:authorities="com.example.farmflow.flutter.printing"
139-->[:printing] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-68
140            android:exported="false"
140-->[:printing] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-37
141            android:grantUriPermissions="true" >
141-->[:printing] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-47
142            <meta-data
142-->[:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-18:53
143                android:name="android.support.FILE_PROVIDER_PATHS"
143-->[:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:17:17-67
144                android:resource="@xml/flutter_printing_file_paths" />
144-->[:open_file_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\open_file_android\intermediates\merged_manifest\debug\AndroidManifest.xml:18:17-50
145        </provider>
146
147        <activity
147-->[:url_launcher_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
148            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
148-->[:url_launcher_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
149            android:exported="false"
149-->[:url_launcher_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
150            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
150-->[:url_launcher_android] C:\Users\<USER>\Desktop\PARMPLOW\FarmFlow\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
151
152        <property
152-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:30:9-32:61
153            android:name="android.adservices.AD_SERVICES_CONFIG"
153-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:31:13-65
154            android:resource="@xml/ga_ad_services_config" />
154-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a09a525a4f095db1ddfaaea27cec8ed6\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:32:13-58
155
156        <provider
156-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
157            android:name="com.google.firebase.provider.FirebaseInitProvider"
157-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
158            android:authorities="com.example.farmflow.firebaseinitprovider"
158-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
159            android:directBootAware="true"
159-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
160            android:exported="false"
160-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
161            android:initOrder="100" />
161-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\b13e451c4617e199a184ef0140130436\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
162
163        <uses-library
163-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d8011ecb2b4abbe6dd1ee65c0a16a62\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
164            android:name="androidx.window.extensions"
164-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d8011ecb2b4abbe6dd1ee65c0a16a62\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
165            android:required="false" />
165-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d8011ecb2b4abbe6dd1ee65c0a16a62\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
166        <uses-library
166-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d8011ecb2b4abbe6dd1ee65c0a16a62\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
167            android:name="androidx.window.sidecar"
167-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d8011ecb2b4abbe6dd1ee65c0a16a62\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
168            android:required="false" />
168-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d8011ecb2b4abbe6dd1ee65c0a16a62\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
169
170        <receiver
170-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:29:9-33:20
171            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
171-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:30:13-85
172            android:enabled="true"
172-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:31:13-35
173            android:exported="false" >
173-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:32:13-37
174        </receiver>
175
176        <service
176-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:35:9-38:40
177            android:name="com.google.android.gms.measurement.AppMeasurementService"
177-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:36:13-84
178            android:enabled="true"
178-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:37:13-35
179            android:exported="false" />
179-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:38:13-37
180        <service
180-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:39:9-43:72
181            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
181-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:40:13-87
182            android:enabled="true"
182-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:41:13-35
183            android:exported="false"
183-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:42:13-37
184            android:permission="android.permission.BIND_JOB_SERVICE" />
184-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8f5ba814afff86cbced61c0d38bd9a3e\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:43:13-69
185
186        <activity
186-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c4729c540afc85a20200e7f28f92586\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
187            android:name="com.google.android.gms.common.api.GoogleApiActivity"
187-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c4729c540afc85a20200e7f28f92586\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
188            android:exported="false"
188-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c4729c540afc85a20200e7f28f92586\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
189            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
189-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c4729c540afc85a20200e7f28f92586\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
190
191        <provider
191-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc7de8790058e921ba0aca1c8afd3a30\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
192            android:name="androidx.startup.InitializationProvider"
192-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc7de8790058e921ba0aca1c8afd3a30\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
193            android:authorities="com.example.farmflow.androidx-startup"
193-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc7de8790058e921ba0aca1c8afd3a30\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
194            android:exported="false" >
194-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc7de8790058e921ba0aca1c8afd3a30\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
195            <meta-data
195-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc7de8790058e921ba0aca1c8afd3a30\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
196                android:name="androidx.emoji2.text.EmojiCompatInitializer"
196-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc7de8790058e921ba0aca1c8afd3a30\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
197                android:value="androidx.startup" />
197-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc7de8790058e921ba0aca1c8afd3a30\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
198            <meta-data
198-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\465fa5597554e8ef5eeea5f4462d0db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
199                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
199-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\465fa5597554e8ef5eeea5f4462d0db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
200                android:value="androidx.startup" />
200-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\465fa5597554e8ef5eeea5f4462d0db4\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
201            <meta-data
201-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
202                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
202-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
203                android:value="androidx.startup" />
203-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
204        </provider>
205
206        <uses-library
206-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\b8db4b3108bb0d810b720b08b0e8e9d6\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
207            android:name="android.ext.adservices"
207-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\b8db4b3108bb0d810b720b08b0e8e9d6\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
208            android:required="false" />
208-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\b8db4b3108bb0d810b720b08b0e8e9d6\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
209
210        <meta-data
210-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0297ec01e5faeda16541291842a915c\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
211            android:name="com.google.android.gms.version"
211-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0297ec01e5faeda16541291842a915c\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
212            android:value="@integer/google_play_services_version" />
212-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0297ec01e5faeda16541291842a915c\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
213
214        <receiver
214-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
215            android:name="androidx.profileinstaller.ProfileInstallReceiver"
215-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
216            android:directBootAware="false"
216-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
217            android:enabled="true"
217-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
218            android:exported="true"
218-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
219            android:permission="android.permission.DUMP" >
219-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
220            <intent-filter>
220-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
221                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
221-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
221-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
222            </intent-filter>
223            <intent-filter>
223-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
224                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
224-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
224-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
225            </intent-filter>
226            <intent-filter>
226-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
227                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
227-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
227-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
228            </intent-filter>
229            <intent-filter>
229-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
230                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
230-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
230-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3c81c8b128e973b4ccdca241823ab3b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
231            </intent-filter>
232        </receiver>
233    </application>
234
235</manifest>
