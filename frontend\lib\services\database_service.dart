import 'package:firebase_database/firebase_database.dart';
import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';

class DatabaseService {
  final FirebaseDatabase _database = FirebaseDatabase.instance;

  // Stream controller for broadcasting schedule updates
  final StreamController<String> _scheduleUpdateController = StreamController<String>.broadcast();

  // Constructor
  DatabaseService() {
    if (!kIsWeb) {
      _database.setPersistenceEnabled(true);
      _database.setPersistenceCacheSizeBytes(10000000); // 10MB cache
      print('DatabaseService initialized with persistence enabled');
    } else {
      print('DatabaseService initialized for web (no persistence)');
    }
    // Test Firebase connection
    _testFirebaseConnection();
  }

  // Test Firebase connection
  Future<void> _testFirebaseConnection() async {
    try {
      print('🔗 Testing Firebase connection...');
      final testRef = _database.ref().child('farmflow').child('connection_test');

      // Test with timeout
      await testRef.set({
        'timestamp': DateTime.now().toIso8601String(),
        'status': 'connected',
        'app_version': '1.0.0'
      }).timeout(Duration(seconds: 5));

      print('✅ Firebase connection successful!');

      // Test read operation
      final readTest = await testRef.get().timeout(Duration(seconds: 5));
      print('✅ Firebase read test successful, data exists: ${readTest.exists}');

      // Clean up test data after 5 seconds
      Future.delayed(Duration(seconds: 5), () {
        testRef.remove().catchError((e) {
          print('⚠️ Error cleaning up test data: $e');
        });
      });
    } catch (e) {
      print('❌ Firebase connection failed: $e');
      print('❌ Error type: ${e.runtimeType}');
      if (e.toString().contains('timeout')) {
        print('⏰ This appears to be a timeout issue - check internet connection');
      } else if (e.toString().contains('permission')) {
        print('🔒 This appears to be a permission issue - check Firebase rules');
      }
      print('Please check your Firebase configuration and internet connection.');
    }
  }

  // Stream for listening to schedule updates
  Stream<String> get scheduleUpdates => _scheduleUpdateController.stream;

  // Check Firebase configuration and connectivity
  Future<Map<String, dynamic>> checkFirebaseStatus() async {
    final status = <String, dynamic>{
      'configured': false,
      'connected': false,
      'canRead': false,
      'canWrite': false,
      'error': null,
    };

    try {
      // Check if Firebase is configured
      status['configured'] = true;
      print('✅ Firebase is configured');

      // Test basic connectivity
      await _database.ref().child('.info').child('connected').get().timeout(
        Duration(seconds: 3),
        onTimeout: () => throw TimeoutException('Connectivity test timeout', Duration(seconds: 3)),
      );
      status['connected'] = true;
      print('✅ Firebase connectivity test passed');

      // Test read permissions
      try {
        await _database.ref().child('farmflow').get().timeout(Duration(seconds: 3));
        status['canRead'] = true;
        print('✅ Firebase read permissions OK');
      } catch (e) {
        print('❌ Firebase read test failed: $e');
      }

      // Test write permissions
      try {
        await _database.ref().child('farmflow').child('test_write').set({
          'timestamp': DateTime.now().toIso8601String(),
          'test': true,
        }).timeout(Duration(seconds: 3));
        status['canWrite'] = true;
        print('✅ Firebase write permissions OK');

        // Clean up test write
        _database.ref().child('farmflow').child('test_write').remove().catchError((e) {
          print('⚠️ Error cleaning up test write: $e');
        });
      } catch (e) {
        print('❌ Firebase write test failed: $e');
      }

    } catch (e) {
      status['error'] = e.toString();
      print('❌ Firebase status check failed: $e');
    }

    return status;
  }

  // Quick connectivity check with mobile-specific handling
  Future<bool> isFirebaseConnected() async {
    try {
      final platform = Platform.operatingSystem;
      print('🔗 Testing Firebase connectivity on $platform...');

      // Use longer timeout for mobile platforms due to potential network delays
      final timeoutDuration = (Platform.isAndroid || Platform.isIOS)
          ? Duration(seconds: 8)
          : Duration(seconds: 3);

      // Try a simple read operation first
      await _database.ref().child('farmflow').child('connection_test').get().timeout(timeoutDuration);
      print('✅ Firebase connectivity confirmed on $platform');
      return true;
    } catch (e) {
      print('❌ Quick connectivity check failed on ${Platform.operatingSystem}: $e');
      print('❌ Error type: ${e.runtimeType}');

      // Provide platform-specific error guidance
      if (Platform.isAndroid || Platform.isIOS) {
        print('📱 Mobile platform detected - this could be a mobile-specific configuration issue');
        if (e.toString().contains('client_id')) {
          print('🔧 Check google-services.json and firebase_options.dart for correct mobile client IDs');
        }
      }

      if (e.toString().contains('timeout')) {
        print('⏰ This appears to be a timeout - possible network or Firebase server issue on mobile');
      } else if (e.toString().contains('permission')) {
        print('🔒 This appears to be a permission issue - check Firebase rules');
      } else if (e.toString().contains('network')) {
        print('🌐 This appears to be a network connectivity issue on mobile');
      } else {
        print('🔧 This appears to be a configuration issue - check Firebase setup for mobile');
      }

      return false;
    }
  }

  // Create sample gates for offline/testing mode
  List<Map<String, dynamic>> _createSampleGates() {
    return [
      {
        'id': 'G001',
        'gateName': 'Sample Gate 001',
        'phoneNumber': '+639123456789',
        'location': 'North Field',
        'description': 'Sample gate for testing',
        'gateStatus': 'closed',
        'isRegistered': true,
        'registrationMethod': 'sample',
        'registeredAt': DateTime.now().toIso8601String(),
        'lastUpdated': DateTime.now().toIso8601String(),
      },
      {
        'id': 'G002',
        'gateName': 'Sample Gate 002',
        'phoneNumber': '+639987654321',
        'location': 'South Field',
        'description': 'Another sample gate',
        'gateStatus': 'open',
        'isRegistered': true,
        'registrationMethod': 'sample',
        'registeredAt': DateTime.now().toIso8601String(),
        'lastUpdated': DateTime.now().toIso8601String(),
      },
    ];
  }

  // Method to close the stream controller
  void closeScheduleUpdates() {
    _scheduleUpdateController.close();
  }

  // Save data for a specific gate
  Future<void> saveGateData(String gateId, Map<String, dynamic> data) async {
    await _database.ref().child('farmflow').child('gates').child(gateId).update(data);
  }

  // Register a new gate
  Future<void> registerGate(String gateId, Map<String, dynamic> gateData) async {
    try {
      await _database.ref().child('farmflow').child('gates').child(gateId).set(gateData);
      print('Gate $gateId registered successfully');
    } catch (e) {
      print('Error registering gate $gateId: $e');
      rethrow;
    }
  }

  // Get all gates from Firebase database with mobile-specific handling
  Future<List<Map<String, dynamic>>> getAllGates() async {
    try {
      final platform = Platform.operatingSystem;
      print('🔍 DatabaseService: Starting getAllGates() on $platform - fetching from Firebase...');

      // Use longer timeout for mobile platforms due to potential network delays
      final timeoutDuration = (Platform.isAndroid || Platform.isIOS)
          ? Duration(seconds: 15)
          : Duration(seconds: 10);

      print('📡 DatabaseService: Querying gates from farmflow/gates (timeout: ${timeoutDuration.inSeconds}s)...');
      final snapshot = await _database.ref().child('farmflow').child('gates').get().timeout(
        timeoutDuration,
        onTimeout: () {
          print('⏰ DatabaseService: Gates query timed out after ${timeoutDuration.inSeconds} seconds on $platform');
          throw TimeoutException('Gates query timeout on $platform', timeoutDuration);
        },
      );
      print('✅ DatabaseService: Firebase query completed on $platform, snapshot exists: ${snapshot.exists}');

      if (snapshot.exists) {
        final data = snapshot.value;
        print('📊 DatabaseService: Raw data type: ${data.runtimeType}');
        print('📊 DatabaseService: Raw data: $data');

        if (data is Map) {
          final Map<dynamic, dynamic> gatesMap = data;
          List<Map<String, dynamic>> gates = [];

          gatesMap.forEach((key, value) {
            print('🚪 Processing gate: $key -> ${value.runtimeType}');
            if (value is Map) {
              try {
                final gateData = Map<String, dynamic>.from(value);
                gateData['id'] = key; // Ensure ID is included
                gates.add(gateData);
                print('✅ Added gate: $key');
              } catch (e) {
                print('❌ Error processing gate $key: $e');
              }
            } else {
              print('⚠️ Skipping non-map gate data for $key: ${value.runtimeType}');
            }
          });

          // Sort gates by ID
          gates.sort((a, b) => (a['id'] ?? '').compareTo(b['id'] ?? ''));
          print('✅ DatabaseService: Successfully loaded ${gates.length} gates from database');
          return gates;
        } else {
          print('⚠️ DatabaseService: Data is not a Map, type: ${data.runtimeType}');
          return [];
        }
      } else {
        print('ℹ️ DatabaseService: No gates found in database (snapshot does not exist)');
        return [];
      }
    } catch (e, stackTrace) {
      print('❌ DatabaseService: Error getting all gates: $e');
      print('❌ DatabaseService: Stack trace: $stackTrace');

      // Try to provide more specific error information
      if (e.toString().contains('permission')) {
        print('🔒 DatabaseService: This appears to be a permission error');
      } else if (e.toString().contains('network')) {
        print('🌐 DatabaseService: This appears to be a network error');
      } else if (e.toString().contains('timeout')) {
        print('⏰ DatabaseService: This appears to be a timeout error');
      }

      // Re-throw the original error to let caller handle it
      print('❌ DatabaseService: getAllGates failed, re-throwing error for caller to handle');
      rethrow;
    }
  }

  // Get all gates with offline fallback
  Future<List<Map<String, dynamic>>> getAllGatesWithFallback() async {
    try {
      // Try to get real data first
      return await getAllGates();
    } catch (e) {
      print('❌ DatabaseService: Real data failed, using sample data: $e');
      return _createSampleGates();
    }
  }

  // Update gate data
  Future<void> updateGateData(String gateId, Map<String, dynamic> updates) async {
    try {
      await _database.ref().child('farmflow').child('gates').child(gateId).update(updates);
      print('Gate $gateId updated successfully');
    } catch (e) {
      print('Error updating gate $gateId: $e');
      rethrow;
    }
  }

  // Delete a gate
  Future<void> deleteGate(String gateId) async {
    try {
      await _database.ref().child('farmflow').child('gates').child(gateId).remove();
      print('Gate $gateId deleted successfully');
    } catch (e) {
      print('Error deleting gate $gateId: $e');
      rethrow;
    }
  }

  // Check if a phone number is already registered
  Future<bool> isPhoneNumberRegistered(String phoneNumber) async {
    try {
      final gates = await getAllGates();
      return gates.any((gate) => gate['phoneNumber']?.toString() == phoneNumber);
    } catch (e) {
      print('Error checking phone number registration: $e');
      return false;
    }
  }

  // Save data to the home node
  Future<void> saveHomeData(Map<String, dynamic> data) async {
    await _database.ref().child('farmflow').child('home').update(data);
  }

  Future<Map<String, dynamic>?> getHomeData() async {
    final snapshot = await _database.ref().child('farmflow').child('home').get();
    if (snapshot.exists) {
      return Map<String, dynamic>.from(snapshot.value as Map);
    }
    return null;
  }

  // Reports Screen Data
  Future<void> saveReportData(Map<String, dynamic> data) async {
    try {
      await _database.ref().child('farmflow').child('reports').push().set(data);
    } catch (e) {
      print('Error saving report data: $e');
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> getReportsData() async {
    try {
      final snapshot = await _database.ref().child('farmflow').child('reports').get();
      if (snapshot.exists) {
        final Map<dynamic, dynamic> data = snapshot.value as Map;
        return data.entries.map((entry) {
          return Map<String, dynamic>.from(entry.value as Map)
            ..['id'] = entry.key;
        }).toList();
      }
      return [];
    } catch (e) {
      print('Error getting reports data: $e');
      rethrow;
    }
  }

  // Schedule Screen Data
  Future<List<Map<String, dynamic>>> getSchedulesData() async {
    final snapshot = await _database.ref().child('farmflow').child('schedules').get();
    if (snapshot.exists) {
      final data = Map<String, dynamic>.from(snapshot.value as Map);
      return data.entries.map((e) {
        final schedule = Map<String, dynamic>.from(e.value);
        schedule['id'] = e.key;
        return schedule;
      }).toList();
    }
    return [];
  }

  // Get schedules for a specific gate
  Future<List<Map<String, dynamic>>> getGateSchedules(String gateId) async {
    final snapshot = await _database.ref().child('farmflow').child('gates').child(gateId).child('schedules').get();
    if (snapshot.exists) {
      final data = Map<String, dynamic>.from(snapshot.value as Map);
      return data.entries.map((e) {
        final schedule = Map<String, dynamic>.from(e.value);
        schedule['id'] = e.key;
        schedule['gateId'] = gateId;
        return schedule;
      }).toList();
    }
    return [];
  }

  Stream<DatabaseEvent> listenToSchedulesData() {
    return _database.ref().child('farmflow').child('schedules').onValue;
  }

  // Listen to schedules for a specific gate
  Stream<DatabaseEvent> listenToGateSchedules(String gateId) {
    return _database.ref().child('farmflow').child('gates').child(gateId).child('schedules').onValue;
  }

  // Get combined schedules from both main and gate-specific collections
  Future<List<Map<String, dynamic>>> getCombinedSchedules() async {
    try {
      final List<Map<String, dynamic>> combinedSchedules = [];

      // Get schedules from main collection
      final mainSnapshot = await _database.ref().child('farmflow').child('schedules').get();
      if (mainSnapshot.exists) {
        final mainData = Map<String, dynamic>.from(mainSnapshot.value as Map);
        for (var entry in mainData.entries) {
          final schedule = Map<String, dynamic>.from(entry.value);
          schedule['id'] = entry.key;
          combinedSchedules.add(schedule);
        }
      }

      // Get all gates
      final gatesSnapshot = await _database.ref().child('farmflow').child('gates').get();
      if (gatesSnapshot.exists) {
        final gates = Map<String, dynamic>.from(gatesSnapshot.value as Map);

        // For each gate, get its schedules
        for (var gateEntry in gates.entries) {
          final gateId = gateEntry.key;
          final gateData = Map<String, dynamic>.from(gateEntry.value);

          if (gateData.containsKey('schedules')) {
            final schedules = Map<String, dynamic>.from(gateData['schedules']);

            for (var scheduleEntry in schedules.entries) {
              final scheduleId = scheduleEntry.key;
              final schedule = Map<String, dynamic>.from(scheduleEntry.value);

              // Check if this schedule is already in the combined list
              final existingIndex = combinedSchedules.indexWhere((s) => s['id'] == scheduleId);

              if (existingIndex >= 0) {
                // Update existing schedule with gate-specific data (which is more accurate)
                combinedSchedules[existingIndex] = {
                  ...combinedSchedules[existingIndex],
                  ...schedule,
                  'id': scheduleId,
                  'gateId': gateId,
                };
              } else {
                // Add new schedule
                schedule['id'] = scheduleId;
                schedule['gateId'] = gateId;
                combinedSchedules.add(schedule);
              }
            }
          }
        }
      }

      return combinedSchedules;
    } catch (e) {
      print('Error getting combined schedules: $e');
      return [];
    }
  }

  Future<void> saveSchedule(Map<String, dynamic> schedule) async {
    await _database.ref().child('farmflow').child('schedules').push().set(schedule);
  }

  // Save schedule for a specific gate
  Future<void> saveGateSchedule(String gateId, Map<String, dynamic> schedule) async {
    final scheduleId = DateTime.now().millisecondsSinceEpoch.toString();
    final scheduleData = {
      ...schedule,
      'scheduleId': scheduleId,
      'gateId': gateId,
      'gateName': 'Gate $gateId',
      'createdAt': DateTime.now().toIso8601String(),
      'status': 'active',
    };

    // Store schedule directly under the gate ID
    await _database.ref().child('farmflow').child('gates').child(gateId).child('schedules').child(scheduleId).set(scheduleData);

    // Also store a reference in the main schedules collection for consistency
    await _database.ref().child('farmflow').child('schedules').child(scheduleId).set(scheduleData);

    print('Saved schedule $scheduleId for gate $gateId in both locations');

    // Notify all listeners that a new schedule has been added
    _scheduleUpdateController.add('$gateId:$scheduleId:added');
  }

  Future<void> updateSchedule(String scheduleId, Map<String, dynamic> data) async {
    await _database.ref().child('farmflow').child('schedules/$scheduleId').update(data);
  }

  // Update schedule for a specific gate
  Future<void> updateGateSchedule(String gateId, String scheduleId, Map<String, dynamic> data) async {
    try {
      final timestamp = DateTime.now().toIso8601String();
      final updateData = {
        ...data,
        'updatedAt': timestamp,
        'gateId': gateId,
        'gateName': 'Gate $gateId',
        'realTimeUpdate': true, // Flag to indicate this was a real-time update
      };

      // Update in gate-specific schedules
      await _database.ref().child('farmflow').child('gates').child(gateId).child('schedules').child(scheduleId).update(updateData);

      // Also update in the main schedules collection for consistency
      await _database.ref().child('farmflow').child('schedules').child(scheduleId).update(updateData);

      print('Successfully updated schedule $scheduleId for Gate $gateId at $timestamp in both locations');
    } catch (e) {
      print('Error updating schedule $scheduleId for Gate $gateId: $e');
      rethrow;
    }
  }

  Future<void> deleteSchedule(String scheduleId) async {
    try {
      // First, get the schedule to find its gate ID
      final scheduleSnapshot = await _database.ref().child('farmflow').child('schedules').child(scheduleId).get();

      if (scheduleSnapshot.exists) {
        final scheduleData = Map<String, dynamic>.from(scheduleSnapshot.value as Map);
        final gateId = scheduleData['gateId'];

        if (gateId != null) {
          // Delete from gate-specific schedules
          await _database.ref().child('farmflow').child('gates').child(gateId).child('schedules').child(scheduleId).remove();

          // Add a record of this deletion to the gate's operation history
          await recordGateOperation(gateId, 'schedule_deleted', {
            'scheduleId': scheduleId,
            'action': 'delete',
            'description': 'Schedule deleted for Gate $gateId',
          });
        }
      }

      // Delete from main schedules collection
      await _database.ref().child('farmflow').child('schedules').child(scheduleId).remove();

      print('Successfully deleted schedule $scheduleId from all locations');
    } catch (e) {
      print('Error deleting schedule $scheduleId: $e');
      rethrow;
    }
  }

  // Delete schedule for a specific gate
  Future<void> deleteGateSchedule(String gateId, String scheduleId) async {
    try {
      // Remove from gate-specific schedules
      await _database.ref().child('farmflow').child('gates').child(gateId).child('schedules').child(scheduleId).remove();

      // Also remove from the main schedules collection for consistency
      await _database.ref().child('farmflow').child('schedules').child(scheduleId).remove();

      // Add a record of this deletion to the gate's operation history
      await recordGateOperation(gateId, 'schedule_deleted', {
        'scheduleId': scheduleId,
        'action': 'delete',
        'description': 'Schedule deleted for Gate $gateId',
      });

      print('Successfully deleted schedule $scheduleId for Gate $gateId from both locations');

      // Notify all listeners that a schedule has been deleted
      _scheduleUpdateController.add('$gateId:$scheduleId:deleted');
    } catch (e) {
      print('Error deleting schedule $scheduleId for Gate $gateId: $e');
      rethrow;
    }
  }

  // Real-time listeners
  Stream<DatabaseEvent> listenToHomeData() {
    return _database.ref().child('farmflow').child('home').onValue;
  }

  Stream<DatabaseEvent> listenToReportsData() {
    return _database.ref().child('farmflow').child('reports').onValue;
  }

  // Listen to gate status changes
  Stream<DatabaseEvent> listenToGateStatus() {
    return _database.ref().child('farmflow').child('home').onValue;
  }

  // Listen to specific gate data
  Stream<DatabaseEvent> listenToGateData(String gateId) {
    return _database.ref().child('farmflow').child('gates').child(gateId).onValue;
  }

  // Listen to all gates data
  Stream<DatabaseEvent> listenToAllGates() {
    return _database.ref().child('farmflow').child('gates').onValue;
  }

  // Listen to all schedules data
  Stream<DatabaseEvent> listenToAllSchedules() {
    return _database.ref().child('farmflow').child('schedules').onValue;
  }

  // Get specific gate data
  Future<Map<String, dynamic>?> getGateData(String gateId) async {
    final snapshot = await _database.ref().child('farmflow').child('gates').child(gateId).get();
    if (snapshot.exists) {
      return Map<String, dynamic>.from(snapshot.value as Map);
    }
    return null;
  }

  // Get all gates data
  Future<Map<String, Map<String, dynamic>>> getAllGatesData() async {
    try {
      final snapshot = await _database.ref().child('farmflow').child('gates').get();
      if (snapshot.exists) {
        final Map<dynamic, dynamic> data = snapshot.value as Map;
        final result = <String, Map<String, dynamic>>{};

        for (var entry in data.entries) {
          final gateId = entry.key as String;
          final gateData = Map<String, dynamic>.from(entry.value as Map);
          result[gateId] = gateData;
        }

        return result;
      }
      return {};
    } catch (e) {
      print('Error getting all gates data: $e');
      return {};
    }
  }



  // Update gate location data
  Future<void> updateGateLocation(String gateId, double latitude, double longitude) async {
    try {
      final timestamp = DateTime.now().toIso8601String();

      // Update location in a standardized structure that matches the Orange Pi
      await _database.ref().child('farmflow').child('gates').child(gateId).update({
        'location': {
          'latitude': latitude,
          'longitude': longitude,
          'updatedAt': timestamp,
          'hasSignal': true
        },
        'locationUpdatedAt': timestamp,
        'locationRequestStatus': 'completed',
        'locationResponseTime': timestamp,
        'requestType': null
      });

      print('Updated location for gate $gateId: $latitude, $longitude');

      // Also record this as a location history entry
      final recordId = '${gateId}_${DateTime.now().millisecondsSinceEpoch}';
      await _database.ref().child('farmflow').child('gates').child(gateId).child('location_history').child(recordId).set({
        'gateId': gateId,
        'locationType': 'coordinates',
        'latitude': latitude,
        'longitude': longitude,
        'timestamp': timestamp,
        'dataType': 'location'
      });
    } catch (e) {
      print('Error updating gate location: $e');
      rethrow;
    }
  }

  // Request location from a gate using command system
  Future<void> requestGateLocation(String gateId) async {
    try {
      print('🌍 Starting location request for gate $gateId');

      // Send LOCATION command to Orange Pi server via Firebase command system
      await _sendLocationCommand(gateId);

      print('✅ Location command sent successfully');

      // Record this operation
      final timestamp = DateTime.now().toIso8601String();
      await recordGateOperation(gateId, 'location_request', {
        'timestamp': timestamp,
        'description': 'Location command sent for Gate $gateId',
      });

    } catch (e) {
      print('❌ Error requesting gate location: $e');
      rethrow;
    }
  }

  /// Send LOCATION command to Orange Pi server via Firebase
  Future<void> _sendLocationCommand(String gateId) async {
    print('🚀 Starting _sendLocationCommand for gate $gateId');

    try {
      // Generate unique command ID
      final commandId = 'cmd_${gateId}_location_${DateTime.now().millisecondsSinceEpoch}';
      print('🆔 Generated command ID: $commandId');

      // Create command data expected by server
      final commandData = {
        'gateId': gateId,
        'action': 'LOCATION',
        'timestamp': DateTime.now().toIso8601String(),
        'source': 'app_location',
        'status': 'pending',
        'userId': 'app_user',
        'commandType': 'location_request',
        'createdAt': DateTime.now().toIso8601String(),
      };

      print('📋 Command data: $commandData');
      print('📍 Writing to path: farmflow/gate_commands/$commandId');

      // Write command to Firebase path monitored by server
      final databaseRef = _database.ref('farmflow/gate_commands/$commandId');
      await databaseRef.set(commandData);

      print('✅ Location command successfully written to Firebase!');
      print('✅ Command sent to server: $commandId');
      print('📤 Action: LOCATION Gate: $gateId');

    } catch (e, stackTrace) {
      print('❌ Failed to send location command to server: $e');
      print('❌ Stack trace: $stackTrace');
      rethrow;
    }
  }

  // Request water flow data from a gate
  Future<void> requestGateWaterflow(String gateId) async {
    try {
      final timestamp = DateTime.now().toIso8601String();

      // Get current gate status to preserve it
      final gateSnapshot = await _database.ref().child('farmflow').child('gates').child(gateId).get();
      final gateData = gateSnapshot.value as Map<dynamic, dynamic>?;
      final currentGateStatus = gateData?['gateStatus'] ?? 'unknown';

      // Update gate to request water flow data without changing gate status
      await _database.ref().child('farmflow').child('gates').child(gateId).update({
        'requestType': 'waterflow',
        'waterflowRequested': true,
        'waterflowRequestTime': timestamp,
        'lastCommand': 'FLOW',
        'lastCommandTime': timestamp,
        'lastOperation': 'manual',
        'lastOperationTime': timestamp,
        'waterflowRequestStatus': 'pending',
        // Explicitly preserve the current gate status
        'gateStatus': currentGateStatus
      });

      print('Water flow data request sent for gate $gateId at $timestamp');

      // Record this operation
      await recordGateOperation(gateId, 'waterflow_request', {
        'timestamp': timestamp,
        'description': 'Water flow data requested for Gate $gateId',
      });
    } catch (e) {
      print('Error requesting gate water flow data: $e');
      rethrow;
    }
  }

  // Get all gates waterflow data
  Future<Map<String, List<Map<String, dynamic>>>> getAllGatesWaterflowData() async {
    try {
      final gatesData = await getAllGatesData();
      final result = <String, List<Map<String, dynamic>>>{};

      for (var gateId in gatesData.keys) {
        final waterflowData = await getGateWaterflowData(gateId);
        if (waterflowData.isNotEmpty) {
          result[gateId] = waterflowData;
        }
      }

      return result;
    } catch (e) {
      print('Error getting all gates waterflow data: $e');
      return {};
    }
  }

  // Get waterflow data for a specific gate
  Future<List<Map<String, dynamic>>> getGateWaterflowData(String gateId) async {
    try {
      final snapshot = await _database.ref().child('farmflow').child('gates').child(gateId).child('waterflow').get();
      if (snapshot.exists) {
        final Map<dynamic, dynamic> data = snapshot.value as Map;
        return data.entries.map((entry) {
          return Map<String, dynamic>.from(entry.value as Map)
            ..['id'] = entry.key;
        }).toList();
      }
      return [];
    } catch (e) {
      print('Error getting gate waterflow data: $e');
      rethrow;
    }
  }

  // Get all gates waterflow reports
  Future<Map<String, List<Map<String, dynamic>>>> getAllGatesWaterflowReports() async {
    try {
      final gatesData = await getAllGatesData();
      final result = <String, List<Map<String, dynamic>>>{};

      for (var gateId in gatesData.keys) {
        final reports = await getGateWaterflowReports(gateId);
        if (reports.isNotEmpty) {
          result[gateId] = reports;
        }
      }

      return result;
    } catch (e) {
      print('Error getting all gates waterflow reports: $e');
      return {};
    }
  }

  // Get waterflow reports for a specific gate
  Future<List<Map<String, dynamic>>> getGateWaterflowReports(String gateId) async {
    try {
      final snapshot = await _database.ref().child('farmflow').child('gates').child(gateId).child('waterflow_reports').get();
      if (snapshot.exists) {
        final Map<dynamic, dynamic> data = snapshot.value as Map;
        return data.entries.map((entry) {
          return Map<String, dynamic>.from(entry.value as Map)
            ..['id'] = entry.key;
        }).toList();
      }
      return [];
    } catch (e) {
      print('Error getting gate waterflow reports: $e');
      return [];
    }
  }



  // Update gate operation status
  Future<void> updateGateOperationStatus(String gateId, String status) async {
    await _database.ref().child('farmflow').child('gates').child(gateId).update({
      'operationStatus': status,
      'lastStatusUpdate': DateTime.now().toIso8601String(),
    });
  }

  // Listen to operations data
  Stream<DatabaseEvent> listenToOperationsData() {
    return _database.ref().child('farmflow').child('operations').onValue;
  }

  // Update gate status
  Future<void> updateGateStatus(String status) async {
    final timestamp = DateTime.now().toIso8601String();

    // Handle pending states
    String displayStatus = status;
    if (status == 'pending_open') {
      displayStatus = 'opening';
    } else if (status == 'pending_close') {
      displayStatus = 'closing';
    }

    await _database.ref().child('farmflow').child('home').update({
      'gateStatus': status,
      'displayStatus': displayStatus,
      'lastUpdated': timestamp,
      'operationStatus': status.startsWith('pending_') ? 'in_progress' : 'completed',
    });
  }

  // Toggle gate status without auto-generating waterflow data
  Future<void> toggleGateStatus(String gateId, String newStatus) async {
    try {
      final timestamp = DateTime.now().toIso8601String();

      // Handle pending states for better UI feedback
      String actualStatus = newStatus;
      String displayStatus = newStatus;
      String operationStatus = 'completed';

      // If this is a direct command, set to pending state first
      if (newStatus == 'open') {
        actualStatus = 'pending_open';
        displayStatus = 'opening';
        operationStatus = 'in_progress';
      } else if (newStatus == 'close' || newStatus == 'closed') {
        actualStatus = 'pending_close';
        displayStatus = 'closing';
        operationStatus = 'in_progress';
      }

      // Update gate status
      await _database.ref().child('farmflow').child('gates').child(gateId).update({
        'gateStatus': actualStatus,
        'displayStatus': displayStatus,
        'lastUpdated': timestamp,
        'lastOperation': 'manual',
        'lastOperationTime': timestamp,
        'operationStatus': operationStatus,
      });

      // Record the operation
      await recordGateManualOperation(gateId, newStatus);

      print('Gate $gateId status toggled to $newStatus without auto-generating waterflow data');

      // If opening the gate, create an empty session entry
      if (newStatus == 'open') {
        final sessionId = DateTime.now().millisecondsSinceEpoch.toString();

        // Create an empty session entry - flow rate will be updated by actual sensor data
        await _database.ref().child('farmflow').child('gates').child(gateId).child('waterflow').child(sessionId).set({
          'sessionId': sessionId,
          'gateId': gateId,
          'timestamp': timestamp,
          'flowRate': 0.0, // Initial value, will be updated by actual sensor data
          'gateStatus': 'open',
          'action': 'initialize',
          'description': 'Session initialized for Gate $gateId, waiting for sensor data',
        });

        // Register this as an active session
        registerActiveSession(gateId, sessionId);
      }
      // If closing the gate, finalize any active session
      else if (newStatus == 'close' && _currentSessions.containsKey(gateId)) {
        final sessionId = _currentSessions[gateId];
        if (sessionId != null) {
          // Get actual total water usage from real sensor data
          final sessionTotal = await getGateSessionTotalWaterUsage(gateId, sessionId);

          // Create a report for the completed session
          await saveGateWaterflowSessionReport(gateId, {
            'sessionId': sessionId,
            'startTime': DateTime.now().subtract(Duration(minutes: 30)).toIso8601String(), // Approximate
            'endTime': timestamp,
            'totalWater': sessionTotal,
            'status': 'completed',
            'closedBy': 'manual',
            'description': 'Session ended by manual close action for Gate $gateId',
          });

          // Unregister the active session
          unregisterActiveSession(gateId);
        }
      }
    } catch (e) {
      print('Error toggling gate status: $e');
      rethrow;
    }
  }

  // Add new operation
  Future<void> addOperation(Map<String, dynamic> operation) async {
    await _database.ref().child('farmflow').child('operations').push().set({
      ...operation,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  // Get all operations
  Future<List<Map<String, dynamic>>> getOperations() async {
    final snapshot = await _database.ref().child('farmflow').child('operations').get();
    if (snapshot.exists) {
      final data = Map<String, dynamic>.from(snapshot.value as Map);
      return data.entries.map((entry) {
        final operation = Map<String, dynamic>.from(entry.value);
        operation['id'] = entry.key;
        return operation;
      }).toList();
    }
    return [];
  }

  // Waterflow Sensor Data
  Future<void> saveWaterflowData(Map<String, dynamic> data) async {
    try {
      final sessionId = data['sessionId'] ?? DateTime.now().millisecondsSinceEpoch.toString();
      await _database.ref().child('farmflow').child('waterflow').push().set({
        ...data,
        'sessionId': sessionId,
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('Error saving waterflow data: $e');
      rethrow;
    }
  }

  // Save waterflow data for a specific gate
  Future<void> saveGateWaterflowData(String gateId, Map<String, dynamic> data) async {
    try {
      final sessionId = data['sessionId'] ?? DateTime.now().millisecondsSinceEpoch.toString();
      final entryId = DateTime.now().millisecondsSinceEpoch.toString();
      final timestamp = DateTime.now().toIso8601String();

      // Get flow rate from data
      final flowRate = (data['flowRate'] as num?)?.toDouble() ?? 0.0;

      // Calculate water volume for this entry (assuming 30 seconds between readings)
      final waterVolume = flowRate * 30; // L/s * seconds = liters

      final waterflowData = {
        ...data,
        'entryId': entryId,
        'gateId': gateId,
        'gateName': 'Gate $gateId',
        'sessionId': sessionId,
        'timestamp': timestamp,
        'waterVolume': waterVolume, // Add calculated water volume
      };

      // Save to gate-specific waterflow data
      await _database.ref().child('farmflow').child('gates').child(gateId).child('waterflow').child(entryId).set(waterflowData);

      // Also save to live_waterflow for graphs
      await _database.ref().child('farmflow').child('live_waterflow').child(gateId).child(entryId).set(waterflowData);

      // Save to historical waterflow data collection
      await _database.ref().child('farmflow').child('waterflow_history').child(gateId).child(entryId).set(waterflowData);

      // Update the gate's total water usage if flow rate is positive
      if (flowRate > 0) {
        await updateGateTotalWaterUsage(gateId, waterVolume);
      }

      // Record this as an operation within the gate's data
      await _database.ref().child('farmflow').child('gates').child(gateId).child('operations').child(entryId).set({
        'operationId': entryId,
        'timestamp': timestamp,
        'type': 'waterflow_recorded',
        'sessionId': sessionId,
        'entryId': entryId,
        'flowRate': flowRate,
        'waterVolume': waterVolume,
        'action': 'record',
        'description': 'Waterflow data recorded for Gate $gateId',
      });
    } catch (e) {
      print('Error saving gate waterflow data: $e');
      rethrow;
    }
  }

  // Update the total water usage for a gate
  Future<void> updateGateTotalWaterUsage(String gateId, double additionalWater) async {
    try {
      if (additionalWater <= 0) {
        return; // Skip if no additional water
      }

      print('Updating total water usage for gate $gateId: +$additionalWater L');

      // Get current total water usage
      final gateSnapshot = await _database.ref().child('farmflow').child('gates').child(gateId).get();
      double currentTotal = 0.0;

      if (gateSnapshot.exists) {
        final gateData = Map<String, dynamic>.from(gateSnapshot.value as Map);
        currentTotal = (gateData['totalWaterUsage'] as num?)?.toDouble() ?? 0.0;
      }

      // Calculate new total
      final newTotal = currentTotal + additionalWater;

      // Update the gate's total water usage
      await _database.ref().child('farmflow').child('gates').child(gateId).update({
        'totalWaterUsage': newTotal,
        'totalWaterUsageCubicMeters': newTotal / 1000.0, // Also store in cubic meters
        'hasReceivedFlowData': true,
        'lastWaterUsageUpdate': DateTime.now().toIso8601String(),
      });

      // Also update the global total water usage
      await updateGlobalTotalWaterUsage(additionalWater);

      print('Updated total water usage for gate $gateId: $currentTotal L -> $newTotal L');
    } catch (e) {
      print('Error updating gate total water usage: $e');
    }
  }

  // Update the global total water usage
  Future<void> updateGlobalTotalWaterUsage(double additionalWater) async {
    try {
      if (additionalWater <= 0) {
        return; // Skip if no additional water
      }

      // Get current global total
      final homeSnapshot = await _database.ref().child('farmflow').child('home').get();
      double currentTotal = 0.0;

      if (homeSnapshot.exists) {
        final homeData = Map<String, dynamic>.from(homeSnapshot.value as Map);
        currentTotal = (homeData['totalWaterUsage'] as num?)?.toDouble() ?? 0.0;
      }

      // Calculate new total
      final newTotal = currentTotal + additionalWater;

      // Update the global total water usage
      await _database.ref().child('farmflow').child('home').update({
        'totalWaterUsage': newTotal,
        'totalWaterUsageCubicMeters': newTotal / 1000.0, // Also store in cubic meters
        'lastWaterUsageUpdate': DateTime.now().toIso8601String(),
      });

      print('Updated global total water usage: $currentTotal L -> $newTotal L');
    } catch (e) {
      print('Error updating global total water usage: $e');
    }
  }

  Future<double> getSessionTotalWaterUsage(String sessionId) async {
    try {
      final snapshot = await _database.ref().child('farmflow').child('waterflow').get();
      if (snapshot.exists) {
        final Map<dynamic, dynamic> data = snapshot.value as Map;
        double total = 0.0;
        for (var entry in data.values) {
          if (entry['sessionId'] == sessionId) {
            total += entry['flowRate'] ?? 0.0;
          }
        }
        return total;
      }
      return 0.0;
    } catch (e) {
      print('Error getting session water usage: $e');
      rethrow;
    }
  }

  // Get total water usage for a specific gate session
  Future<double> getGateSessionTotalWaterUsage(String gateId, String sessionId) async {
    try {
      final snapshot = await _database.ref().child('farmflow').child('gates').child(gateId).child('waterflow').get();
      if (snapshot.exists) {
        final Map<dynamic, dynamic> data = snapshot.value as Map;
        double total = 0.0;
        for (var entry in data.values) {
          if (entry['sessionId'] == sessionId) {
            total += entry['flowRate'] ?? 0.0;
          }
        }
        return total;
      }
      return 0.0;
    } catch (e) {
      print('Error getting gate session water usage: $e');
      rethrow;
    }
  }

  Stream<DatabaseEvent> listenToWaterflowData() {
    return _database.ref().child('farmflow').child('waterflow').onValue;
  }

  // Listen to waterflow data for a specific gate
  Stream<DatabaseEvent> listenToGateWaterflowData(String gateId) {
    return _database.ref().child('farmflow').child('gates').child(gateId).child('waterflow').onValue;
  }



  // Listen to live waterflow data for a specific gate (specifically for graphs)
  Stream<DatabaseEvent> listenToLiveGateWaterflowData(String gateId) {
    return _database.ref().child('farmflow').child('live_waterflow').child(gateId).onValue;
  }

  // Listen to all gates' live waterflow data (for combined graphs)
  Stream<DatabaseEvent> listenToAllLiveWaterflowData() {
    return _database.ref().child('farmflow').child('live_waterflow').onValue;
  }

  // Listen to historical waterflow data for a specific gate
  Stream<DatabaseEvent> listenToGateHistoricalWaterflowData(String gateId) {
    return _database.ref().child('farmflow').child('waterflow_history').child(gateId).onValue;
  }

  // Listen to all gates' historical waterflow data
  Stream<DatabaseEvent> listenToAllHistoricalWaterflowData() {
    return _database.ref().child('farmflow').child('waterflow_history').onValue;
  }

  // Listen to waterflow reports for a specific gate
  Stream<DatabaseEvent> listenToGateWaterflowReports(String gateId) {
    return _database.ref().child('farmflow').child('gates').child(gateId).child('waterflow_reports').onValue;
  }



  Future<List<Map<String, dynamic>>> getWaterflowData() async {
    try {
      final snapshot = await _database.ref().child('farmflow').child('waterflow').get();
      if (snapshot.exists) {
        final Map<dynamic, dynamic> data = snapshot.value as Map;
        return data.entries.map((entry) {
          return Map<String, dynamic>.from(entry.value as Map)
            ..['id'] = entry.key;
        }).toList();
      }
      return [];
    } catch (e) {
      print('Error getting waterflow data: $e');
      rethrow;
    }
  }



  // Get live waterflow data for a specific gate (specifically for graphs)
  Future<List<Map<String, dynamic>>> getLiveGateWaterflowData(String gateId) async {
    try {
      final snapshot = await _database.ref().child('farmflow').child('live_waterflow').child(gateId).get();
      if (snapshot.exists) {
        final Map<dynamic, dynamic> data = snapshot.value as Map;
        final result = data.entries.map((entry) {
          return Map<String, dynamic>.from(entry.value as Map)
            ..['id'] = entry.key;
        }).toList();

        // Sort by timestamp
        result.sort((a, b) {
          try {
            final aTime = DateTime.parse(a['timestamp'].toString());
            final bTime = DateTime.parse(b['timestamp'].toString());
            return aTime.compareTo(bTime); // Ascending order for graphs
          } catch (e) {
            print('Error parsing timestamp: $e');
            return 0;
          }
        });

        return result;
      }
      return [];
    } catch (e) {
      print('Error getting live gate waterflow data: $e');
      return [];
    }
  }

  // Get all gates' live waterflow data (for combined graphs)
  Future<Map<String, List<Map<String, dynamic>>>> getAllLiveWaterflowData() async {
    try {
      print('Getting all live waterflow data...');
      final snapshot = await _database.ref().child('farmflow').child('live_waterflow').get();
      if (snapshot.exists) {
        final Map<dynamic, dynamic> data = snapshot.value as Map;
        final result = <String, List<Map<String, dynamic>>>{};

        for (var entry in data.entries) {
          final gateId = entry.key as String;
          final gateData = Map<dynamic, dynamic>.from(entry.value as Map);

          print('Processing live waterflow data for gate $gateId with ${gateData.length} entries');

          final waterflowEntries = gateData.entries.map((e) {
            return Map<String, dynamic>.from(e.value as Map)
              ..['id'] = e.key;
          }).toList();

          // Include all entries, even with zero flow rate
          final validEntries = waterflowEntries;

          print('Found ${validEntries.length} waterflow entries for gate $gateId');

          // Sort by timestamp with robust parsing
          validEntries.sort((a, b) {
            try {
              DateTime aTime, bTime;

              // Try to parse timestamp from the data
              try {
                String aTimestamp = a['timestamp']?.toString() ?? '';
                if (aTimestamp.contains('|')) {
                  aTimestamp = aTimestamp.split('|')[0];
                }
                aTime = DateTime.parse(aTimestamp);
              } catch (e) {
                // Try to extract timestamp from record ID
                try {
                  final recordId = a['id']?.toString() ?? '';
                  if (recordId.contains('_')) {
                    final timestampPart = recordId.split('_').last;
                    aTime = DateTime.fromMillisecondsSinceEpoch(int.parse(timestampPart) * 1000);
                  } else {
                    aTime = DateTime.now();
                  }
                } catch (e2) {
                  aTime = DateTime.now();
                }
              }

              // Same for the second timestamp
              try {
                String bTimestamp = b['timestamp']?.toString() ?? '';
                if (bTimestamp.contains('|')) {
                  bTimestamp = bTimestamp.split('|')[0];
                }
                bTime = DateTime.parse(bTimestamp);
              } catch (e) {
                try {
                  final recordId = b['id']?.toString() ?? '';
                  if (recordId.contains('_')) {
                    final timestampPart = recordId.split('_').last;
                    bTime = DateTime.fromMillisecondsSinceEpoch(int.parse(timestampPart) * 1000);
                  } else {
                    bTime = DateTime.now();
                  }
                } catch (e2) {
                  bTime = DateTime.now();
                }
              }

              return aTime.compareTo(bTime); // Ascending order for graphs
            } catch (e) {
              print('Error sorting waterflow entries: $e');
              return 0;
            }
          });

          // Only add gates with valid entries to the result
          if (validEntries.isNotEmpty) {
            result[gateId] = validEntries;
          }
        }

        return result;
      }
      return {};
    } catch (e) {
      print('Error getting all live waterflow data: $e');
      return {};
    }
  }

  // Calculate total water usage for a specific gate from all waterflow records
  Future<double> calculateGateTotalWaterUsage(String gateId) async {
    try {
      print('Calculating total water usage for gate $gateId from all waterflow records...');

      // First, check if the gate has a stored totalWaterUsage field
      // This is the most accurate as it's cumulative and maintained by the system
      final gateSnapshot = await _database.ref().child('farmflow').child('gates').child(gateId).get();
      if (gateSnapshot.exists) {
        final gateData = Map<String, dynamic>.from(gateSnapshot.value as Map);
        final storedTotal = (gateData['totalWaterUsage'] as num?)?.toDouble() ?? 0.0;
        final hasReceivedFlowData = gateData['hasReceivedFlowData'] == true;

        print('Stored total water usage for gate $gateId: $storedTotal L');
        print('Gate has received flow data: $hasReceivedFlowData');

        // If we have a stored total and the gate has received flow data, use it
        if (hasReceivedFlowData && storedTotal > 0) {
          // Update the gate's totalWaterUsage field to ensure it's up to date
          await _database.ref().child('farmflow').child('gates').child(gateId).update({
            'totalWaterUsage': storedTotal,
            'totalWaterUsageCubicMeters': storedTotal / 1000.0,
            'lastWaterUsageUpdate': DateTime.now().toIso8601String(),
          });

          return storedTotal;
        }
      }

      // If no stored total, check waterflow reports which contain session totals
      final reportsSnapshot = await _database.ref().child('farmflow').child('gates').child(gateId).child('waterflow_reports').get();
      double totalFromReports = 0.0;

      if (reportsSnapshot.exists) {
        final Map<dynamic, dynamic> reports = reportsSnapshot.value as Map;
        print('Found ${reports.length} waterflow reports for gate $gateId');

        // Sum up the total water from all reports
        for (var entry in reports.entries) {
          final report = Map<String, dynamic>.from(entry.value as Map);
          final totalWater = (report['totalWater'] as num?)?.toDouble() ?? 0.0;

          // Only add if it's a valid number
          if (totalWater > 0) {
            totalFromReports += totalWater;
          }
        }

        print('Total water usage from reports for gate $gateId: $totalFromReports L');

        // If we have valid report data, use it and update the stored total
        if (totalFromReports > 0) {
          // Update the gate's totalWaterUsage field
          await _database.ref().child('farmflow').child('gates').child(gateId).update({
            'totalWaterUsage': totalFromReports,
            'totalWaterUsageCubicMeters': totalFromReports / 1000.0,
            'hasReceivedFlowData': true,
            'lastWaterUsageUpdate': DateTime.now().toIso8601String(),
          });

          return totalFromReports;
        }
      }

      // If no reports or reports have zero total, check raw waterflow records
      final waterflowSnapshot = await _database.ref().child('farmflow').child('gates').child(gateId).child('waterflow').get();
      double totalFromRecords = 0.0;
      int validRecordsCount = 0;

      if (waterflowSnapshot.exists) {
        final Map<dynamic, dynamic> data = waterflowSnapshot.value as Map;
        print('Found ${data.length} waterflow records for gate $gateId');

        // Calculate total from valid records only (non-zero flow rate)
        for (var entry in data.entries) {
          final record = Map<String, dynamic>.from(entry.value as Map);
          final flowRate = (record['flowRate'] as num?)?.toDouble() ?? 0.0;
          final totalWater = (record['totalWater'] as num?)?.toDouble() ?? 0.0;
          final waterVolume = (record['waterVolume'] as num?)?.toDouble() ?? 0.0;

          // First check if the record has a waterVolume field (most accurate)
          if (waterVolume > 0) {
            totalFromRecords += waterVolume;
            validRecordsCount++;
          }
          // Then check if the record has a totalWater field
          else if (totalWater > 0) {
            totalFromRecords += totalWater;
            validRecordsCount++;
          }
          // Otherwise calculate from flow rate
          else if (flowRate > 0) {
            // Each record represents approximately 30 seconds of flow
            totalFromRecords += flowRate * 30; // L/s * seconds = liters
            validRecordsCount++;
          }
        }

        print('Found $validRecordsCount valid records with non-zero flow rate or total water');
        print('Total water usage calculated from valid records for gate $gateId: $totalFromRecords L');

        if (totalFromRecords > 0) {
          // Update the gate's totalWaterUsage field
          await _database.ref().child('farmflow').child('gates').child(gateId).update({
            'totalWaterUsage': totalFromRecords,
            'totalWaterUsageCubicMeters': totalFromRecords / 1000.0,
            'hasReceivedFlowData': true,
            'lastWaterUsageUpdate': DateTime.now().toIso8601String(),
          });

          return totalFromRecords;
        }
      }

      // If we get here, we couldn't find any valid water usage data
      // Return the highest value we found from any source
      final highestTotal = totalFromReports > totalFromRecords ? totalFromReports : totalFromRecords;

      if (highestTotal > 0) {
        // Update the gate's totalWaterUsage field with the highest value found
        await _database.ref().child('farmflow').child('gates').child(gateId).update({
          'totalWaterUsage': highestTotal,
          'totalWaterUsageCubicMeters': highestTotal / 1000.0,
          'lastWaterUsageUpdate': DateTime.now().toIso8601String(),
        });
      }

      return highestTotal;
    } catch (e) {
      print('Error calculating gate total water usage: $e');
      return 0.0;
    }
  }

  // Calculate total water usage for all gates from all waterflow records
  Future<Map<String, double>> calculateAllGatesTotalWaterUsage() async {
    try {
      print('Calculating total water usage for all gates from all waterflow records...');

      // Get all gates
      final gatesSnapshot = await _database.ref().child('farmflow').child('gates').get();
      final result = <String, double>{};

      if (gatesSnapshot.exists) {
        final Map<dynamic, dynamic> gates = gatesSnapshot.value as Map;

        // Calculate total for each gate
        for (var entry in gates.entries) {
          final gateId = entry.key as String;
          final totalWater = await calculateGateTotalWaterUsage(gateId);
          result[gateId] = totalWater;
        }
      }

      return result;
    } catch (e) {
      print('Error calculating all gates total water usage: $e');
      return {};
    }
  }

  // Get all gates from the database as a map
  Future<Map<String, Map<String, dynamic>>> getAllGatesAsMap() async {
    try {
      print('Getting all gates as map...');
      final snapshot = await _database.ref().child('farmflow').child('gates').get();

      if (snapshot.exists) {
        final Map<dynamic, dynamic> data = snapshot.value as Map;
        final result = <String, Map<String, dynamic>>{};

        for (var entry in data.entries) {
          final gateId = entry.key as String;
          final gateData = Map<String, dynamic>.from(entry.value as Map);
          result[gateId] = gateData;
        }

        print('Found ${result.length} gates');
        return result;
      }

      return {};
    } catch (e) {
      print('Error getting all gates as map: $e');
      return {};
    }
  }

  // Get waterflow records for a specific gate
  Future<List<Map<String, dynamic>>> getGateWaterflowRecords(String gateId) async {
    try {
      print('Getting waterflow records for gate $gateId...');
      final snapshot = await _database.ref().child('farmflow').child('gates').child(gateId).child('waterflow').get();

      if (snapshot.exists) {
        final Map<dynamic, dynamic> data = snapshot.value as Map;

        final waterflowRecords = data.entries.map((e) {
          final record = Map<String, dynamic>.from(e.value as Map);
          record['id'] = e.key;
          record['gateId'] = gateId;
          return record;
        }).toList();

        // Include all records, even with zero flow rate
        print('Found ${waterflowRecords.length} total waterflow records for gate $gateId');

        // If there are no records with flow rate > 0, create a default record
        if (waterflowRecords.isEmpty) {
          final defaultRecord = {
            'id': 'default_${DateTime.now().millisecondsSinceEpoch}',
            'gateId': gateId,
            'flowRate': 0.0,
            'timestamp': DateTime.now().toIso8601String(),
            'status': 'unknown',
            'sessionId': 'default_session',
            'description': 'No water flow data available yet'
          };
          waterflowRecords.add(defaultRecord);
        }

        return waterflowRecords;
      }

      return [];
    } catch (e) {
      print('Error getting waterflow records for gate $gateId: $e');
      return [];
    }
  }

  // Get historical waterflow records for a specific gate
  Future<List<Map<String, dynamic>>> getGateHistoricalWaterflowRecords(String gateId) async {
    try {
      print('Getting historical waterflow records for gate $gateId...');
      final snapshot = await _database.ref().child('farmflow').child('waterflow_history').child(gateId).get();

      if (snapshot.exists) {
        final Map<dynamic, dynamic> data = snapshot.value as Map;

        final waterflowRecords = data.entries.map((e) {
          final record = Map<String, dynamic>.from(e.value as Map);
          record['id'] = e.key;
          record['gateId'] = gateId;
          return record;
        }).toList();

        // Sort by timestamp (oldest first for historical analysis)
        waterflowRecords.sort((a, b) {
          try {
            final aTime = DateTime.parse(a['timestamp'].toString());
            final bTime = DateTime.parse(b['timestamp'].toString());
            return aTime.compareTo(bTime);
          } catch (e) {
            return 0;
          }
        });

        print('Found ${waterflowRecords.length} historical waterflow records for gate $gateId');
        return waterflowRecords;
      }

      return [];
    } catch (e) {
      print('Error getting historical waterflow records for gate $gateId: $e');
      return [];
    }
  }

  // Get all gates historical waterflow records
  Future<Map<String, List<Map<String, dynamic>>>> getAllGatesHistoricalWaterflowRecords() async {
    try {
      print('Getting historical waterflow records for all gates...');
      final snapshot = await _database.ref().child('farmflow').child('waterflow_history').get();

      if (snapshot.exists) {
        final Map<dynamic, dynamic> data = snapshot.value as Map;
        final result = <String, List<Map<String, dynamic>>>{};

        for (var entry in data.entries) {
          final gateId = entry.key as String;
          final gateData = Map<dynamic, dynamic>.from(entry.value as Map);

          final waterflowRecords = gateData.entries.map((e) {
            final record = Map<String, dynamic>.from(e.value as Map);
            record['id'] = e.key;
            record['gateId'] = gateId;
            return record;
          }).toList();

          // Sort by timestamp (oldest first for historical analysis)
          waterflowRecords.sort((a, b) {
            try {
              final aTime = DateTime.parse(a['timestamp'].toString());
              final bTime = DateTime.parse(b['timestamp'].toString());
              return aTime.compareTo(bTime);
            } catch (e) {
              return 0;
            }
          });

          result[gateId] = waterflowRecords;
        }

        print('Found historical waterflow records for ${result.length} gates');
        return result;
      }

      return {};
    } catch (e) {
      print('Error getting all gates historical waterflow records: $e');
      return {};
    }
  }

   // Waterflow Session Reports
  Future<List<Map<String, dynamic>>> getWaterflowSessions() async {
    try {
      final snapshot = await _database.ref().child('farmflow').child('waterflow').get();
      if (snapshot.exists) {
        final Map<dynamic, dynamic> data = snapshot.value as Map;
        final sessions = <String, Map<String, dynamic>>{};

        for (var entry in data.values) {
          final sessionId = entry['sessionId'];
          if (!sessions.containsKey(sessionId)) {
            sessions[sessionId] = {
              'sessionId': sessionId,
              'startTime': entry['timestamp'],
              'endTime': entry['timestamp'],
              'totalWater': 0.0,
              'gateStatus': 'open',
            };
          }

          final session = sessions[sessionId]!;
          session['totalWater'] = (session['totalWater'] as double) + (entry['flowRate'] ?? 0.0);
          session['endTime'] = entry['timestamp'];
        }

        return sessions.values.toList()
          ..sort((a, b) => DateTime.parse(b['endTime']).compareTo(DateTime.parse(a['endTime'])));
      }
      return [];
    } catch (e) {
      print('Error getting waterflow sessions: $e');
      rethrow;
    }
  }

  // Get waterflow sessions for a specific gate
  Future<List<Map<String, dynamic>>> getGateWaterflowSessions(String gateId) async {
    try {
      final snapshot = await _database.ref().child('farmflow').child('gates').child(gateId).child('waterflow').get();
      if (snapshot.exists) {
        final Map<dynamic, dynamic> data = snapshot.value as Map;
        final sessions = <String, Map<String, dynamic>>{};
        bool hasValidData = false;

        for (var entry in data.values) {
          final sessionId = entry['sessionId'];
          final flowRate = (entry['flowRate'] as num?)?.toDouble() ?? 0.0;

          // Skip entries with zero flow rate
          if (flowRate <= 0) {
            continue;
          }

          hasValidData = true;

          if (!sessions.containsKey(sessionId)) {
            sessions[sessionId] = {
              'sessionId': sessionId,
              'gateId': gateId,
              'startTime': entry['timestamp'],
              'endTime': entry['timestamp'],
              'totalWater': 0.0,
              'gateStatus': 'open',
              'hasValidData': true,
            };
          }

          final session = sessions[sessionId]!;
          session['totalWater'] = (session['totalWater'] as double) + flowRate;
          session['endTime'] = entry['timestamp'];
        }

        print('Gate $gateId has valid waterflow data: $hasValidData');
        print('Found ${sessions.length} valid waterflow sessions for gate $gateId');

        // Update the gate's hasReceivedFlowData flag if we found valid data
        if (hasValidData) {
          await _database.ref().child('farmflow').child('gates').child(gateId).update({
            'hasReceivedFlowData': true,
          });
        }

        return sessions.values.toList()
          ..sort((a, b) => DateTime.parse(b['endTime']).compareTo(DateTime.parse(a['endTime'])));
      }
      return [];
    } catch (e) {
      print('Error getting gate waterflow sessions: $e');
      return [];
    }
  }

  Future<void> saveWaterflowSessionReport(Map<String, dynamic> report) async {
    try {
      await _database.ref().child('farmflow').child('waterflow_reports').push().set({
        ...report,
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('Error saving waterflow report: $e');
      rethrow;
    }
  }

  // Save waterflow session report for a specific gate
  Future<void> saveGateWaterflowSessionReport(String gateId, Map<String, dynamic> report) async {
    try {
      final reportId = report['sessionId'] ?? DateTime.now().millisecondsSinceEpoch.toString();
      final reportData = {
        ...report,
        'reportId': reportId,
        'gateId': gateId,
        'gateName': 'Gate $gateId',
        'timestamp': DateTime.now().toIso8601String(),
        'status': report['status'] ?? 'completed',
      };

      // Save only to gate-specific waterflow reports
      await _database.ref().child('farmflow').child('gates').child(gateId).child('waterflow_reports').child(reportId).set(reportData);

      // Record this as an operation within the gate's data
      await _database.ref().child('farmflow').child('gates').child(gateId).child('operations').child(reportId).set({
        'operationId': reportId,
        'timestamp': DateTime.now().toIso8601String(),
        'type': 'waterflow_report_created',
        'reportId': reportId,
        'sessionId': report['sessionId'],
        'totalWater': report['totalWater'] ?? 0.0,
        'action': 'create_report',
        'description': 'Waterflow session report created for Gate $gateId',
      });
    } catch (e) {
      print('Error saving gate waterflow report: $e');
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> getWaterflowReports() async {
    try {
      final snapshot = await _database.ref().child('farmflow').child('waterflow_reports').get();
      if (snapshot.exists) {
        final Map<dynamic, dynamic> data = snapshot.value as Map;
        return data.entries.map((entry) {
          return Map<String, dynamic>.from(entry.value as Map)
            ..['id'] = entry.key;
        }).toList()
          ..sort((a, b) => DateTime.parse(b['timestamp']).compareTo(DateTime.parse(a['timestamp'])));
      }
      return [];
    } catch (e) {
      print('Error getting waterflow reports: $e');
      rethrow;
    }
  }



  // Record manual gate operation
  Future<void> recordManualOperation(String action) async {
    try {
      await _database.ref().child('farmflow').child('operations').push().set({
        'timestamp': DateTime.now().toIso8601String(),
        'type': 'manual',
        'action': action,
        'status': 'success',
        'description': 'Gate ${action}ed manually by user',
      });
    } catch (e) {
      print('Error recording manual operation: $e');
      rethrow;
    }
  }

  // Record manual operation for a specific gate
  Future<void> recordGateManualOperation(String gateId, String action) async {
    try {
      final operationId = DateTime.now().millisecondsSinceEpoch.toString();
      final operationData = {
        'operationId': operationId,
        'timestamp': DateTime.now().toIso8601String(),
        'type': 'manual',
        'action': action,
        'gateId': gateId,
        'gateName': 'Gate $gateId',
        'status': 'success',
        'description': 'Gate $gateId ${action}ed manually by user',
      };

      // Record only in gate-specific operations
      await _database.ref().child('farmflow').child('gates').child(gateId).child('operations').child(operationId).set(operationData);
    } catch (e) {
      print('Error recording gate manual operation: $e');
      rethrow;
    }
  }

  // Record any operation for a specific gate
  Future<void> recordGateOperation(String gateId, String type, Map<String, dynamic> details) async {
    try {
      final operationId = DateTime.now().millisecondsSinceEpoch.toString();
      final operationData = {
        'operationId': operationId,
        'timestamp': DateTime.now().toIso8601String(),
        'type': type,
        'gateId': gateId,
        'gateName': 'Gate $gateId',
        'status': details['status'] ?? 'success',
        'action': details['action'] ?? 'unknown',
        'description': details['description'] ?? 'Operation performed on Gate $gateId',
        ...details,
      };

      // Record only in gate-specific operations
      await _database.ref().child('farmflow').child('gates').child(gateId).child('operations').child(operationId).set(operationData);
    } catch (e) {
      print('Error recording gate operation: $e');
      rethrow;
    }
  }

  // Get operations for a specific gate
  Future<List<Map<String, dynamic>>> getGateOperations(String gateId) async {
    try {
      final snapshot = await _database.ref().child('farmflow').child('gates').child(gateId).child('operations').get();
      if (snapshot.exists) {
        final Map<dynamic, dynamic> data = snapshot.value as Map;
        return data.entries.map((entry) {
          final operation = Map<String, dynamic>.from(entry.value);
          operation['id'] = entry.key;
          return operation;
        }).toList()
          ..sort((a, b) => DateTime.parse(b['timestamp']).compareTo(DateTime.parse(a['timestamp'])));
      }
      return [];
    } catch (e) {
      print('Error getting gate operations: $e');
      rethrow;
    }
  }

  // Listen to operations for a specific gate
  Stream<DatabaseEvent> listenToGateOperations(String gateId) {
    return _database.ref().child('farmflow').child('gates').child(gateId).child('operations').onValue;
  }

  // Current active sessions map
  final Map<String, String> _currentSessions = {};

  // Execute a scheduled action for a gate
  Future<void> executeScheduledAction(String gateId, String scheduleId, String action) async {
    try {
      final timestamp = DateTime.now().toIso8601String();

      print('Executing scheduled action: $action for Gate $gateId (Schedule ID: $scheduleId)');

      // Update the gate status - this is the key operation that changes the gate state
      try {
        // Use update for atomic updates with a transaction to ensure consistency
        await _database.ref().child('farmflow').child('gates').child(gateId).update({
          'gateStatus': action,
          'lastUpdated': timestamp,
          'lastOperation': 'scheduled',
          'lastOperationTime': timestamp,
          'lastAction': action,
          'lastActionTime': timestamp,
          'scheduledExecutionId': scheduleId,
          'realTimeUpdate': true, // Flag to indicate this was a real-time update
          'scheduledExecutionTime': timestamp,
        });

        print('Successfully updated gate data for Gate $gateId with status: $action');

        // Verify the update was successful by reading back the data
        final verifySnapshot = await _database.ref().child('farmflow').child('gates').child(gateId).get();
        if (verifySnapshot.exists) {
          final data = Map<String, dynamic>.from(verifySnapshot.value as Map);
          final currentStatus = data['gateStatus'];
          print('Verified gate status is now: $currentStatus (expected: $action)');

          if (currentStatus != action) {
            print('WARNING: Gate status verification failed! Expected $action but got $currentStatus');
            // Try to update again
            await _database.ref().child('farmflow').child('gates').child(gateId).update({
              'gateStatus': action,
              'lastUpdated': DateTime.now().toIso8601String(),
              'verificationRetry': true,
            });
          }
        }
      } catch (e) {
        print('Error updating gate data: $e');
        rethrow;
      }

      // Also update the global home data to ensure consistency
      try {
        // Use update for atomic updates
        await _database.ref().child('farmflow').child('home').update({
          'gateStatus': action,
          'lastUpdated': timestamp,
          'lastOperation': 'scheduled',
          'lastOperationTime': timestamp,
          'lastAction': action,
          'lastActionTime': timestamp,
          'lastGateId': gateId,
          'scheduledExecutionId': scheduleId,
          'realTimeUpdate': true, // Flag to indicate this was a real-time update
          'scheduledExecutionTime': timestamp,
        });

        print('Successfully updated home data with gate status: $action');

        // Verify the home data update
        final verifyHomeSnapshot = await _database.ref().child('farmflow').child('home').get();
        if (verifyHomeSnapshot.exists) {
          final homeData = Map<String, dynamic>.from(verifyHomeSnapshot.value as Map);
          final homeStatus = homeData['gateStatus'];
          print('Verified home status is now: $homeStatus (expected: $action)');
        }
      } catch (e) {
        print('Error updating home data: $e');
        // Continue execution even if home data update fails
      }

      // Mark the schedule as completed
      await updateGateSchedule(gateId, scheduleId, {
        'isCompleted': true,
        'executedAt': timestamp,
        'executionStatus': 'success',
      });

      // Record the operation
      await recordGateOperation(gateId, 'scheduled_execution', {
        'scheduleId': scheduleId,
        'action': action,
        'status': 'success',
        'timestamp': timestamp,
        'description': 'Scheduled $action executed for Gate $gateId',
      });

      // If the action is 'open', create a new waterflow session
      if (action == 'open') {
        final sessionId = DateTime.now().millisecondsSinceEpoch.toString();
        await saveGateWaterflowData(gateId, {
          'sessionId': sessionId,
          'flowRate': 0.0,
          'gateStatus': 'open',
          'action': 'initialize',
          'description': 'Session started by scheduled action for Gate $gateId',
          'scheduledExecutionId': scheduleId,
        });
      } else if (action == 'close' && _currentSessions.containsKey(gateId)) {
        // If closing the gate and there's an active session, finalize it
        final sessionId = _currentSessions[gateId];
        if (sessionId != null) {
          final sessionTotal = await getGateSessionTotalWaterUsage(gateId, sessionId);

          // Create a report for the completed session
          await saveGateWaterflowSessionReport(gateId, {
            'sessionId': sessionId,
            'startTime': DateTime.now().subtract(Duration(hours: 1)).toIso8601String(), // Approximate
            'endTime': timestamp,
            'totalWater': sessionTotal,
            'status': 'completed',
            'closedBy': 'schedule',
            'scheduleId': scheduleId,
            'description': 'Session ended by scheduled close action for Gate $gateId',
          });

          // Remove from active sessions
          _currentSessions.remove(gateId);
        }
      }

      print('Successfully executed scheduled $action for Gate $gateId at ${DateTime.now()}');
    } catch (e) {
      print('Error executing scheduled action: $e');

      // Record the failed operation
      await recordGateOperation(gateId, 'scheduled_execution_failed', {
        'scheduleId': scheduleId,
        'action': action,
        'status': 'failed',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
        'description': 'Failed to execute scheduled $action for Gate $gateId',
      });

      // Try to mark the schedule as failed
      try {
        await updateGateSchedule(gateId, scheduleId, {
          'executionStatus': 'failed',
          'errorMessage': e.toString(),
          'errorTime': DateTime.now().toIso8601String(),
        });
      } catch (updateError) {
        print('Error updating schedule status: $updateError');
      }

      rethrow;
    }
  }

  // Map to track schedule listeners by gateId
  final Map<String, StreamSubscription<DatabaseEvent>> _scheduleListeners = {};

  // Register an active session for a gate
  void registerActiveSession(String gateId, String sessionId) {
    _currentSessions[gateId] = sessionId;
    print('Registered active session $sessionId for Gate $gateId');
  }

  // Unregister an active session for a gate
  void unregisterActiveSession(String gateId) {
    if (_currentSessions.containsKey(gateId)) {
      final sessionId = _currentSessions.remove(gateId);
      print('Unregistered active session $sessionId for Gate $gateId');
    }
  }

  // Get the active session ID for a gate
  String? getActiveSessionId(String gateId) {
    return _currentSessions[gateId];
  }

  // Start real-time listeners for all gates' schedules
  Future<void> startRealTimeScheduleListeners() async {
    print('Starting real-time schedule listeners at ${DateTime.now().toIso8601String()}');

    // First, stop any existing listeners
    stopRealTimeScheduleListeners();

    try {
      // Get all gates
      final gatesSnapshot = await _database.ref().child('farmflow').child('gates').get();
      if (!gatesSnapshot.exists) {
        print('No gates found in database');
        return;
      }

      final gates = Map<String, dynamic>.from(gatesSnapshot.value as Map);
      print('Found ${gates.length} gates to set up schedule listeners');

      // For each gate, set up a listener for its schedules
      for (var gateEntry in gates.entries) {
        final gateId = gateEntry.key;
        _setupGateScheduleListener(gateId);
      }

      // Also set up a listener for new gates
      _database.ref().child('farmflow').child('gates').onChildAdded.listen((event) {
        if (event.snapshot.exists) {
          final gateId = event.snapshot.key;
          if (gateId != null && !_scheduleListeners.containsKey(gateId)) {
            print('New gate detected: $gateId. Setting up schedule listener.');
            _setupGateScheduleListener(gateId);
          }
        }
      });

      // Start a timer to check all schedules every 1 second for maximum responsiveness
      Timer.periodic(Duration(seconds: 1), (timer) {
        // Only log at specific intervals to reduce console spam
        final now = DateTime.now();
        if (now.second == 0 && now.minute % 5 == 0) {  // Log every 5 minutes
          print('Schedule checker running: ${now.toString().split('.').first}');
        }
        checkAllGatesSchedules();
      });

    } catch (e) {
      print('Error setting up real-time schedule listeners: $e');
    }
  }

  // Check schedules for all gates
  Future<void> checkAllGatesSchedules() async {
    try {
      // Get all gates
      final gatesSnapshot = await _database.ref().child('farmflow').child('gates').get();
      if (!gatesSnapshot.exists) {
        print('No gates found in database for schedule check');
        return;
      }

      final gates = Map<String, dynamic>.from(gatesSnapshot.value as Map);
      // Don't log every check with 1-second interval to reduce log spam
      // Only log occasionally to reduce console output
      if (DateTime.now().second == 0) {  // Log only at the start of each minute
        print('Checking schedules for ${gates.length} gates (minute mark)');
      }

      // For each gate, check its schedules
      for (var gateEntry in gates.entries) {
        final gateId = gateEntry.key;
        final gateData = Map<String, dynamic>.from(gateEntry.value);

        if (gateData.containsKey('schedules')) {
          final schedulesSnapshot = await _database.ref().child('farmflow').child('gates').child(gateId).child('schedules').get();
          if (schedulesSnapshot.exists) {
            _processGateSchedules(gateId, schedulesSnapshot);
          }
        }
      }
    } catch (e) {
      print('Error checking all gates schedules: $e');
    }
  }

  // Set up a real-time listener for a specific gate's schedules
  void _setupGateScheduleListener(String gateId) {
    // Cancel any existing listener for this gate
    _scheduleListeners[gateId]?.cancel();

    // Set up a new listener
    final schedulesRef = _database.ref().child('farmflow').child('gates').child(gateId).child('schedules');
    final subscription = schedulesRef.onValue.listen((event) {
      if (event.snapshot.exists) {
        print('Schedule change detected for Gate $gateId. Checking schedules...');
        _processGateSchedules(gateId, event.snapshot);
      }
    });

    _scheduleListeners[gateId] = subscription;
    print('Set up real-time schedule listener for Gate $gateId');
  }

  // Process schedules for a specific gate
  Future<void> _processGateSchedules(String gateId, DataSnapshot snapshot) async {
    try {
      if (!snapshot.exists) return;

      final schedules = Map<String, dynamic>.from(snapshot.value as Map);
      final now = DateTime.now();

      // Only log if there are pending schedules to reduce log spam with 1-second interval
      final pendingSchedules = schedules.values.where((s) => s['isCompleted'] != true).length;
      if (pendingSchedules > 0) {
        print('Processing $pendingSchedules pending schedules for Gate $gateId');
      }

      for (var scheduleEntry in schedules.entries) {
        final scheduleId = scheduleEntry.key;
        final schedule = Map<String, dynamic>.from(scheduleEntry.value);

        // Skip if already completed
        if (schedule['isCompleted'] == true) {
          print('Schedule $scheduleId is already completed, skipping');
          continue;
        }

        // Skip if already executing
        if (schedule['isExecuting'] == true) {
          print('Schedule $scheduleId is already executing, skipping');
          continue;
        }

        // Check if it's time to execute this schedule using the isScheduleDue method
        if (isScheduleDue(schedule['dateTime'])) {  // Within 1 minute of scheduled time
          print('Schedule $scheduleId is due for execution NOW');

          try {
            // Mark as executing
            await updateGateSchedule(gateId, scheduleId, {
              'isExecuting': true,
              'executionStartedAt': now.toIso8601String(),
            });

            // Execute the action
            final action = schedule['action'] ?? 'open';
            print('Executing scheduled action: $action for Gate $gateId (Schedule ID: $scheduleId)');

            // Update the gate status - this is the key operation that changes the gate state
            final timestamp = now.toIso8601String();

            // First, update the specific gate data
            await _database.ref().child('farmflow').child('gates').child(gateId).update({
              'gateStatus': action,
              'lastUpdated': timestamp,
              'lastOperation': 'scheduled',
              'lastOperationTime': timestamp,
              'lastAction': action,
              'lastActionTime': timestamp,
              'scheduledExecutionId': scheduleId,
              'realTimeUpdate': true, // Flag to indicate this was a real-time update
              'scheduledExecutionTime': timestamp,
            });

            // Also update the global home data to ensure consistency across the app
            await _database.ref().child('farmflow').child('home').update({
              'gateStatus': action,
              'lastUpdated': timestamp,
              'lastOperation': 'scheduled',
              'lastOperationTime': timestamp,
              'lastAction': action,
              'lastActionTime': timestamp,
              'lastGateId': gateId,
              'scheduledExecutionId': scheduleId,
              'realTimeUpdate': true,
              'scheduledExecutionTime': timestamp,
            });

            print('Successfully updated gate status to $action for Gate $gateId');

            // Mark the schedule as completed
            await updateGateSchedule(gateId, scheduleId, {
              'isCompleted': true,
              'isExecuting': false,
              'executedAt': now.toIso8601String(),
              'executionStatus': 'success',
            });

            // Record the operation
            await recordGateOperation(gateId, 'scheduled_execution', {
              'scheduleId': scheduleId,
              'action': action,
              'status': 'success',
              'timestamp': now.toIso8601String(),
              'description': 'Scheduled $action executed for Gate $gateId',
            });

            // Handle recurring schedules
            if (schedule['isRecurring'] == true) {
              await _createNextRecurringSchedule(gateId, scheduleId, schedule);
            }
          } catch (e) {
            print('Error executing schedule $scheduleId: $e');

            try {
              // Mark the schedule as failed
              await updateGateSchedule(gateId, scheduleId, {
                'isExecuting': false,
                'executionStatus': 'failed',
                'executionError': e.toString(),
                'executionErrorTime': now.toIso8601String(),
              });
            } catch (updateError) {
              print('Error updating schedule status: $updateError');
            }
          }
        } else {
          // Calculate time until execution
          final scheduledTime = DateTime.parse(schedule['dateTime']);
          final timeUntilExecution = scheduledTime.difference(now);

          if (timeUntilExecution.isNegative) {
            // Schedule is in the past
            if (timeUntilExecution.inMinutes < -10) {
              // If it's more than 10 minutes in the past, mark as missed
              print('Schedule $scheduleId is more than 10 minutes past due, marking as missed');
              await updateGateSchedule(gateId, scheduleId, {
                'status': 'missed',
                'missedAt': now.toIso8601String(),
              });
            }
          } else {
            // Schedule is in the future
            final minutesUntil = timeUntilExecution.inMinutes;
            if (minutesUntil < 5) {
              print('Schedule $scheduleId will execute in $minutesUntil minutes');
            }
          }
        }
      }
    } catch (e) {
      print('Error processing schedules for Gate $gateId: $e');
    }
  }

  // Create the next occurrence of a recurring schedule
  Future<void> _createNextRecurringSchedule(String gateId, String scheduleId, Map<String, dynamic> schedule) async {
    try {
      final scheduledDateTime = DateTime.parse(schedule['dateTime']);
      final recurringType = schedule['recurringType'] ?? 'daily';
      DateTime nextDateTime;

      // Calculate next occurrence based on recurring type
      switch (recurringType) {
        case 'daily':
          nextDateTime = scheduledDateTime.add(Duration(days: 1));
        case 'weekly':
          nextDateTime = scheduledDateTime.add(Duration(days: 7));
        case 'monthly':
          // Add approximately one month
          nextDateTime = DateTime(
            scheduledDateTime.year,
            scheduledDateTime.month + 1,
            scheduledDateTime.day,
            scheduledDateTime.hour,
            scheduledDateTime.minute,
          );
        default:
          nextDateTime = scheduledDateTime.add(Duration(days: 1));
      }

      // Create a new schedule for the next occurrence
      final newSchedule = Map<String, dynamic>.from(schedule);
      newSchedule.remove('isCompleted');
      newSchedule.remove('executedAt');
      newSchedule.remove('isExecuting');
      newSchedule.remove('executionStartedAt');
      newSchedule.remove('executionStatus');
      newSchedule['dateTime'] = nextDateTime.toIso8601String();
      newSchedule['createdFromRecurring'] = true;
      newSchedule['previousScheduleId'] = scheduleId;
      newSchedule['createdAt'] = DateTime.now().toIso8601String();

      await saveGateSchedule(gateId, newSchedule);
      print('Created new recurring schedule for $recurringType at ${nextDateTime.toIso8601String()}');
    } catch (e) {
      print('Error creating next recurring schedule: $e');
    }
  }

  // Stop all real-time schedule listeners
  void stopRealTimeScheduleListeners() {
    for (var subscription in _scheduleListeners.values) {
      subscription.cancel();
    }
    _scheduleListeners.clear();
    print('Stopped all real-time schedule listeners');
  }

  // Check if a schedule is due for execution
  bool isScheduleDue(String scheduledTimeString) {
    try {
      final now = DateTime.now();
      final scheduledTime = DateTime.parse(scheduledTimeString);

      // Calculate time difference in seconds
      final difference = now.difference(scheduledTime).inSeconds;

      // Schedule is due if it's within 1 second (before or after) for more precise execution
      // This works well with the 500ms check interval
      return difference.abs() <= 1;
    } catch (e) {
      print('Error checking if schedule is due: $e');
      return false;
    }
  }

  // Check if the schedule time exactly matches the current time
  bool isExactTimeMatch(DateTime scheduledTime) {
    final now = DateTime.now();

    // Check if hours, minutes, and seconds match
    return scheduledTime.hour == now.hour &&
           scheduledTime.minute == now.minute &&
           (now.second - scheduledTime.second).abs() <= 1;
  }

  // Dispose all resources
  void dispose() {
    stopRealTimeScheduleListeners();
    _scheduleUpdateController.close();
  }

  // Check for due schedules and execute them
  Future<void> checkAndExecuteSchedules() async {
    try {
      print('Checking for schedules to execute at ${DateTime.now().toIso8601String()}');

      // Get all gates
      final gatesSnapshot = await _database.ref().child('farmflow').child('gates').get();
      if (!gatesSnapshot.exists) {
        print('No gates found in database');
        return;
      }

      final gates = Map<String, dynamic>.from(gatesSnapshot.value as Map);
      print('Found ${gates.length} gates to check for schedules');

      // For each gate, check schedules
      for (var gateEntry in gates.entries) {
        final gateId = gateEntry.key;
        final gateData = Map<String, dynamic>.from(gateEntry.value);

        // Skip if no schedules
        if (!gateData.containsKey('schedules')) {
          print('Gate $gateId has no schedules');
          continue;
        }

        final schedules = Map<String, dynamic>.from(gateData['schedules']);
        print('Gate $gateId has ${schedules.length} schedules');

        // Check each schedule
        for (var scheduleEntry in schedules.entries) {
          final scheduleId = scheduleEntry.key;
          final schedule = Map<String, dynamic>.from(scheduleEntry.value);

          // Skip if already completed
          if (schedule['isCompleted'] == true) {
            print('Schedule $scheduleId is already completed, skipping');
            continue;
          }

          // Skip if already executing to prevent duplicate executions
          if (schedule['isExecuting'] == true) {
            print('Schedule $scheduleId is already executing, skipping');
            continue;
          }

          final scheduledDateTime = DateTime.parse(schedule['dateTime']);
          final now = DateTime.now();

          // Check if the schedule is due for execution (exact time match)
          // Using our precise time matching function for exact execution
          if (isExactTimeMatch(scheduledDateTime)) {

            final action = schedule['action'] ?? 'open';
            final currentTime = DateTime.now();
            print('EXECUTING SCHEDULE: $scheduleId at EXACT TIME: ${currentTime.hour}:${currentTime.minute}:${currentTime.second}');
            print('Scheduled time was: ${scheduledDateTime.hour}:${scheduledDateTime.minute}:${scheduledDateTime.second}');
            print('Action: $action for Gate: $gateId');

            // Mark as executing to prevent duplicate executions
            try {
              await updateGateSchedule(gateId, scheduleId, {
                'isExecuting': true,
                'executionStartedAt': now.toIso8601String(),
              });
            } catch (e) {
              print('Error marking schedule as executing: $e');
              continue;
            }

            try {
              // Execute the scheduled action
              await executeScheduledAction(gateId, scheduleId, action);
              print('Successfully executed schedule $scheduleId for Gate $gateId');

              // Handle recurring schedules
              if (schedule['isRecurring'] == true) {
                final recurringType = schedule['recurringType'] ?? 'daily';
                DateTime nextDateTime;

                // Calculate next occurrence based on recurring type
                switch (recurringType) {
                  case 'daily':
                    nextDateTime = scheduledDateTime.add(Duration(days: 1));
                  case 'weekly':
                    nextDateTime = scheduledDateTime.add(Duration(days: 7));
                  case 'monthly':
                    // Add approximately one month (30 days)
                    nextDateTime = DateTime(
                      scheduledDateTime.year,
                      scheduledDateTime.month + 1,
                      scheduledDateTime.day,
                      scheduledDateTime.hour,
                      scheduledDateTime.minute,
                    );
                  default:
                    nextDateTime = scheduledDateTime.add(Duration(days: 1));
                }

                // Create a new schedule for the next occurrence
                final newSchedule = Map<String, dynamic>.from(schedule);
                newSchedule.remove('isCompleted');
                newSchedule.remove('executedAt');
                newSchedule.remove('isExecuting');
                newSchedule.remove('executionStartedAt');
                newSchedule.remove('executionStatus');
                newSchedule['dateTime'] = nextDateTime.toIso8601String();
                newSchedule['createdFromRecurring'] = true;
                newSchedule['previousScheduleId'] = scheduleId;
                newSchedule['createdAt'] = now.toIso8601String();

                await saveGateSchedule(gateId, newSchedule);
                print('Created new recurring schedule for $recurringType at ${nextDateTime.toIso8601String()}');
              }
            } catch (e) {
              print('Error executing schedule $scheduleId: $e');

              // Reset executing status in case of error
              try {
                await updateGateSchedule(gateId, scheduleId, {
                  'isExecuting': false,
                  'executionError': e.toString(),
                  'executionErrorTime': DateTime.now().toIso8601String(),
                });
              } catch (updateError) {
                print('Error updating schedule execution status: $updateError');
              }
            }
          } else {
            // Calculate time until execution
            final timeUntilExecution = scheduledDateTime.difference(now);
            if (timeUntilExecution.isNegative) {
              // Schedule is in the past but not within the 5-minute window
              if (timeUntilExecution.inMinutes < -10) {
                // If it's more than 10 minutes in the past, mark as missed
                print('Schedule $scheduleId is more than 10 minutes past due, marking as missed');
                await updateGateSchedule(gateId, scheduleId, {
                  'status': 'missed',
                  'missedAt': now.toIso8601String(),
                });
              }
            } else {
              // Schedule is in the future
              print('Schedule $scheduleId will execute in ${timeUntilExecution.inMinutes} minutes');
            }
          }
        }
      }
    } catch (e) {
      print('Error checking schedules: $e');
    }
  }

  // Farmer Methods

  // Get all farmers
  Future<List<Map<String, dynamic>>> getAllFarmers() async {
    try {
      final snapshot = await _database.ref().child('farmflow').child('farmers').get();
      if (snapshot.exists) {
        final Map<dynamic, dynamic> data = snapshot.value as Map;
        return data.entries.map((entry) {
          return Map<String, dynamic>.from(entry.value as Map)
            ..['id'] = entry.key;
        }).toList();
      }
      return [];
    } catch (e) {
      print('Error getting farmers: $e');
      return [];
    }
  }

  // Listen to farmers
  Stream<DatabaseEvent> listenToFarmers() {
    return _database.ref().child('farmflow').child('farmers').onValue;
  }

  // Save farmer
  Future<String> saveFarmer(Map<String, dynamic> farmer) async {
    try {
      final ref = _database.ref().child('farmflow').child('farmers').push();
      await ref.set(farmer);
      return ref.key ?? '';
    } catch (e) {
      print('Error saving farmer: $e');
      rethrow;
    }
  }

  // Update farmer
  Future<void> updateFarmer(String farmerId, Map<String, dynamic> data) async {
    try {
      await _database.ref().child('farmflow').child('farmers').child(farmerId).update(data);
    } catch (e) {
      print('Error updating farmer: $e');
      rethrow;
    }
  }

  // Alias for updateFarmer (for compatibility)
  Future<void> updateFarmerData(String farmerId, Map<String, dynamic> data) async {
    return updateFarmer(farmerId, data);
  }

  // Delete farmer
  Future<void> deleteFarmer(String farmerId) async {
    try {
      await _database.ref().child('farmflow').child('farmers').child(farmerId).remove();
    } catch (e) {
      print('Error deleting farmer: $e');
      rethrow;
    }
  }

  // Get farmer by ID
  Future<Map<String, dynamic>?> getFarmer(String farmerId) async {
    try {
      final snapshot = await _database.ref().child('farmflow').child('farmers').child(farmerId).get();
      if (snapshot.exists) {
        return Map<String, dynamic>.from(snapshot.value as Map)..['id'] = farmerId;
      }
      return null;
    } catch (e) {
      print('Error getting farmer: $e');
      rethrow;
    }
  }

  // Search farmers by name
  Future<List<Map<String, dynamic>>> searchFarmersByName(String query) async {
    try {
      final allFarmers = await getAllFarmers();
      if (query.isEmpty) {
        return allFarmers;
      }

      final lowercaseQuery = query.toLowerCase();
      return allFarmers.where((farmer) {
        final name = (farmer['name'] as String? ?? '').toLowerCase();
        return name.contains(lowercaseQuery);
      }).toList();
    } catch (e) {
      print('Error searching farmers: $e');
      return [];
    }
  }

  // Farmer Payment Methods

  // Get all farmer payments
  Future<List<Map<String, dynamic>>> getAllFarmerPayments() async {
    try {
      final snapshot = await _database.ref().child('farmflow').child('farmer_payments').get();
      if (snapshot.exists) {
        final Map<dynamic, dynamic> data = snapshot.value as Map;
        return data.entries.map((entry) {
          return Map<String, dynamic>.from(entry.value as Map)
            ..['id'] = entry.key;
        }).toList();
      }
      return [];
    } catch (e) {
      print('Error getting farmer payments: $e');
      return [];
    }
  }

  // Listen to farmer payments
  Stream<DatabaseEvent> listenToFarmerPayments() {
    return _database.ref().child('farmflow').child('farmer_payments').onValue;
  }

  // Save farmer payment
  Future<void> saveFarmerPayment(Map<String, dynamic> payment) async {
    try {
      await _database.ref().child('farmflow').child('farmer_payments').push().set(payment);
    } catch (e) {
      print('Error saving farmer payment: $e');
      rethrow;
    }
  }

  // Update farmer payment
  Future<void> updateFarmerPayment(String paymentId, Map<String, dynamic> data) async {
    try {
      await _database.ref().child('farmflow').child('farmer_payments').child(paymentId).update(data);
    } catch (e) {
      print('Error updating farmer payment: $e');
      rethrow;
    }
  }

  // Delete farmer payment
  Future<void> deleteFarmerPayment(String paymentId) async {
    try {
      await _database.ref().child('farmflow').child('farmer_payments').child(paymentId).remove();
    } catch (e) {
      print('Error deleting farmer payment: $e');
      rethrow;
    }
  }

  // Get farmer payment by ID
  Future<Map<String, dynamic>?> getFarmerPayment(String paymentId) async {
    try {
      final snapshot = await _database.ref().child('farmflow').child('farmer_payments').child(paymentId).get();
      if (snapshot.exists) {
        return Map<String, dynamic>.from(snapshot.value as Map)..['id'] = paymentId;
      }
      return null;
    } catch (e) {
      print('Error getting farmer payment: $e');
      rethrow;
    }
  }

  // Get payments for a specific farmer
  Future<List<Map<String, dynamic>>> getFarmerPaymentsByFarmerId(String farmerId) async {
    try {
      final allPayments = await getAllFarmerPayments();
      return allPayments.where((payment) => payment['farmerId'] == farmerId).toList();
    } catch (e) {
      print('Error getting farmer payments by farmer ID: $e');
      return [];
    }
  }
}