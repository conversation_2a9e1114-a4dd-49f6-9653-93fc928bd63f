import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';
import 'firebase_options.dart';
import 'services/database_service.dart';
import 'services/schedule_service.dart';
import 'services/notification_service_stub.dart';
import 'home_screen.dart';
import 'schedule_screen.dart';
import 'report_screen.dart';
import 'theme/app_theme.dart';
import 'theme/theme_provider.dart';
import 'screens/settings_screen.dart';
import 'screens/dashboard_screen.dart';
import 'services/schedule_checker_service.dart';
import 'sample_data/sample_farmer_payments.dart';
import 'sample_data/sample_farmers.dart';
import 'widgets/notification_permission_dialog.dart';

void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // Set preferred orientations for mobile
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Initialize Firebase with mobile-specific handling
    try {
      print('🔧 Initializing Firebase for platform: ${Platform.operatingSystem}');

      // Check if Firebase is already initialized
      if (Firebase.apps.isEmpty) {
        await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
        );
        print('✅ Firebase initialized successfully for ${Platform.operatingSystem}');

        // Configure Firebase for mobile platforms
        if (Platform.isAndroid || Platform.isIOS) {
          print('📱 Configuring Firebase for mobile platform');
          final database = FirebaseDatabase.instance;

          // Enable offline persistence for mobile
          try {
            database.setPersistenceEnabled(true);
            database.setPersistenceCacheSizeBytes(10000000); // 10MB cache
            print('✅ Mobile offline persistence enabled');
          } catch (persistenceError) {
            print('⚠️ Mobile offline persistence setup failed: $persistenceError');
          }

          // Test mobile connection with longer timeout
          try {
            print('🔗 Testing mobile Firebase connection...');
            await database.ref().child('farmflow').child('mobile_test').set({
              'platform': Platform.operatingSystem,
              'timestamp': DateTime.now().toIso8601String(),
              'status': 'mobile_connected'
            }).timeout(Duration(seconds: 15)); // Increased timeout for mobile
            print('✅ Mobile Firebase connection test successful');
          } catch (testError) {
            print('⚠️ Mobile Firebase connection test failed: $testError');
            print('⚠️ This might indicate database rules or network connectivity issues');

            // Try a simple read test
            try {
              print('🔍 Attempting read-only test...');
              await database.ref().child('farmflow').child('gates').get().timeout(Duration(seconds: 10));
              print('✅ Mobile Firebase read test successful');
            } catch (readError) {
              print('❌ Mobile Firebase read test also failed: $readError');
            }
          }
        }

      } else {
        print('✅ Firebase already initialized');
      }
    } catch (e) {
      print('❌ Error initializing Firebase: $e');
      print('❌ Error type: ${e.runtimeType}');
      print('❌ Platform: ${Platform.operatingSystem}');

      // Provide specific error guidance
      if (e.toString().contains('client_id')) {
        print('🔧 This appears to be a client ID configuration issue for mobile');
      } else if (e.toString().contains('api_key')) {
        print('🔧 This appears to be an API key configuration issue');
      } else if (e.toString().contains('network')) {
        print('🌐 This appears to be a network connectivity issue on mobile');
      }

      // Don't rethrow if it's just a duplicate app error
      if (!e.toString().contains('duplicate-app')) {
        rethrow;
      } else {
        print('ℹ️  Firebase already initialized (duplicate-app error ignored)');
      }
    }

    // Initialize notification service
    final notificationService = NotificationService();
    await notificationService.init();

    // Check if notification permission has been requested before
    final prefs = await SharedPreferences.getInstance();
    final hasRequestedPermission = prefs.getBool('hasRequestedNotificationPermission') ?? false;

    runApp(MyApp(
      hasRequestedPermission: hasRequestedPermission,
    ));
  } catch (e) {
    print('❌ Error during app initialization: $e');
    // Don't show error app for duplicate-app since it's harmless
    if (e.toString().contains('duplicate-app')) {
      print('ℹ️  Ignoring duplicate-app error, continuing with normal app');
      runApp(MyApp(hasRequestedPermission: false));
    } else {
      print('⚠️  Showing error app due to serious initialization error');
      runApp(ErrorApp(error: e.toString()));
    }
  }
}

// Error fallback app
class ErrorApp extends StatelessWidget {
  final String error;

  const ErrorApp({Key? key, required this.error}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, color: Colors.red, size: 64),
                SizedBox(height: 16),
                Text(
                  'Error Initializing App',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 8),
                Text(
                  error,
                  style: TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    main();
                  },
                  child: Text('Retry'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class MyApp extends StatefulWidget {
  final bool hasRequestedPermission;

  const MyApp({
    Key? key,
    required this.hasRequestedPermission,
  }) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late final NotificationService _notificationService;
  bool _shouldShowPermissionDialog = false;

  @override
  void initState() {
    super.initState();
    _notificationService = NotificationService();

    // Set flag to show dialog after a delay
    if (!widget.hasRequestedPermission) {
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _shouldShowPermissionDialog = true;
          });
        }
      });
    }
  }

  // Handle permission result without BuildContext issues
  void _handlePermissionResult(bool hasPermission, BuildContext context) {
    // Save permission status in shared preferences
    SharedPreferences.getInstance().then((prefs) {
      prefs.setBool('hasRequestedNotificationPermission', true);

      // Show confirmation if permission granted
      if (hasPermission && mounted) {
        // Use a post-frame callback to show the snackbar after the dialog is dismissed
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Notifications enabled!'),
                backgroundColor: Colors.green,
              ),
            );
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        Provider<DatabaseService>(
          create: (_) => DatabaseService(),
        ),
        ProxyProvider<DatabaseService, ScheduleService>(
          update: (_, databaseService, __) => ScheduleService(databaseService),
        ),
        Provider<NotificationService>(
          create: (_) => _notificationService,
        ),
        ChangeNotifierProvider(
          create: (_) => ThemeProvider(),
        ),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, _) => MaterialApp(
          title: 'FarmFlow',
          theme: themeProvider.themeData,
          home: Builder(
            builder: (context) {
              // Show notification permission dialog if flag is set
              if (_shouldShowPermissionDialog) {
                // Reset flag to prevent showing dialog multiple times
                _shouldShowPermissionDialog = false;

                // Show the dialog
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (dialogContext) => NotificationPermissionDialog(
                      onPermissionResult: (hasPermission) {
                        // Handle permission result synchronously
                        _handlePermissionResult(hasPermission, context);
                      },
                    ),
                  );
                });
              }
              return const DashboardScreen();
            },
          ),
          debugShowCheckedModeBanner: false,
        ),
      ),
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({Key? key}) : super(key: key);

  @override
  State<MainScreen> createState() => MainScreenState();
}

class MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;
  late ScheduleService _scheduleService;
  final ScheduleCheckerService _scheduleChecker = ScheduleCheckerService();

  final List<Widget> _screens = [
    const HomeScreen(selectedGateId: 'G001'),
    ReportScreen(),
    ScheduleScreen(),
    SettingsScreen(),
  ];

  @override
  void initState() {
    super.initState();
    // Get the database service
    final databaseService = Provider.of<DatabaseService>(context, listen: false);

    // Get the notification service
    final notificationService = Provider.of<NotificationService>(context, listen: false);

    // Start real-time schedule listeners for immediate database updates
    databaseService.startRealTimeScheduleListeners();

    // Initialize and start the schedule service as a backup
    _scheduleService = Provider.of<ScheduleService>(context, listen: false);
    _scheduleService.startScheduleChecker();

    // Initialize and start the schedule checker service as another backup
    _scheduleChecker.setDatabaseService(databaseService);
    _scheduleChecker.startScheduleChecker();

    // Set up listeners for notifications
    _setupNotificationListeners(databaseService, notificationService);

    // Add sample farmer payments for testing (only in development)
    _addSampleFarmerPayments(databaseService);
  }

  void _setupNotificationListeners(DatabaseService databaseService, NotificationService notificationService) {
    // Listen for schedule completions
    databaseService.listenToAllSchedules().listen((event) {
      if (event.snapshot.exists) {
        final schedules = Map<String, dynamic>.from(event.snapshot.value as Map);

        for (var scheduleData in schedules.values) {
          if (scheduleData is Map &&
              scheduleData['isCompleted'] == true &&
              scheduleData['notified'] != true) {

            // Send notification for completed schedule
            final gateId = scheduleData['gateId']?.toString() ?? 'Unknown';
            final action = scheduleData['action']?.toString() ?? 'open';

            notificationService.showScheduleCompletedNotification(gateId, action);

            // Mark as notified to avoid duplicate notifications
            if (scheduleData['id'] != null) {
              databaseService.updateSchedule(
                scheduleData['id'].toString(),
                {'notified': true}
              );
            }
          }
        }
      }
    });

    // Listen for payment due dates
    databaseService.listenToFarmerPayments().listen((event) {
      if (event.snapshot.exists) {
        final payments = Map<String, dynamic>.from(event.snapshot.value as Map);

        for (var paymentData in payments.values) {
          if (paymentData is Map &&
              paymentData['status'] == 'pending' &&
              paymentData['notified'] != true) {

            try {
              final farmerName = paymentData['farmerName']?.toString() ?? 'Unknown';
              final amountDue = double.tryParse(paymentData['amountDue']?.toString() ?? '0') ?? 0.0;
              final dueDateStr = paymentData['dueDate']?.toString();

              if (dueDateStr != null) {
                final dueDate = DateTime.parse(dueDateStr);
                final now = DateTime.now();

                // If due date is within 2 days, send notification
                if (dueDate.difference(now).inDays <= 2) {
                  notificationService.showPaymentDueNotification(farmerName, amountDue, dueDate);

                  // Schedule notification for 1 day before due date
                  notificationService.schedulePaymentDueNotification(farmerName, amountDue, dueDate);

                  // Mark as notified to avoid duplicate notifications
                  if (paymentData['id'] != null) {
                    databaseService.updateFarmerPayment(
                      paymentData['id'].toString(),
                      {'notified': true}
                    );
                  }
                }
              }
            } catch (e) {
              print('Error processing payment notification: $e');
            }
          }
        }
      }
    });

    // Listen for gate status changes
    databaseService.listenToAllGates().listen((event) {
      if (event.snapshot.exists) {
        final gates = Map<String, dynamic>.from(event.snapshot.value as Map);

        for (var gateId in gates.keys) {
          final gateData = gates[gateId];

          if (gateData is Map &&
              gateData['lastNotified'] != gateData['lastUpdated']) {

            final isOpen = gateData['gateStatus'] == 'open';
            final lastOperation = gateData['lastOperation']?.toString() ?? 'unknown';

            // Only notify for automatic or remote operations, not manual ones from the app
            if (lastOperation != 'manual') {
              notificationService.showGateStatusChangeNotification(gateId, isOpen);

              // Mark as notified to avoid duplicate notifications
              databaseService.updateGateData(gateId, {
                'lastNotified': gateData['lastUpdated']
              });
            }
          }
        }
      }
    });
  }

  // Helper method to add sample farmer payments
  Future<void> _addSampleFarmerPayments(DatabaseService databaseService) async {
    try {
      // Check if farmer payments already exist
      final existingPayments = await databaseService.getAllFarmerPayments();
      if (existingPayments.isEmpty) {
        // Only add sample data if no payments exist
        await addSampleFarmerPaymentsToDatabase(databaseService);
      }

      // Check if farmers already exist
      final existingFarmers = await databaseService.getAllFarmers();
      if (existingFarmers.isEmpty) {
        // Only add sample data if no farmers exist
        await addSampleFarmersToDatabase(databaseService);
      }
    } catch (e) {
      print('Error adding sample data: $e');
    }
  }

  @override
  void dispose() {
    // Get the database service
    final databaseService = Provider.of<DatabaseService>(context, listen: false);

    // Stop all services
    _scheduleService.stopScheduleChecker();
    _scheduleChecker.stopScheduleChecker();
    databaseService.stopRealTimeScheduleListeners();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        backgroundColor: AppTheme.backgroundColor,
        selectedItemColor: AppTheme.primaryColor,
        unselectedItemColor: Colors.grey,
        items: [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.history),
            label: 'Reports',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.schedule),
            label: 'Schedule',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ],
      ),
    );
  }
}

// Add sample farmer payments to the database
Future<void> addSampleFarmerPaymentsToDatabase(DatabaseService databaseService) async {
  try {
    final samplePayments = getSampleFarmerPayments();
    for (var payment in samplePayments) {
      await databaseService.saveFarmerPayment(payment);
    }
    print('Added ${samplePayments.length} sample farmer payments');
  } catch (e) {
    print('Error adding sample farmer payments: $e');
  }
}

// Add sample farmers to the database
Future<void> addSampleFarmersToDatabase(DatabaseService databaseService) async {
  try {
    final sampleFarmers = getSampleFarmers();
    for (var farmer in sampleFarmers) {
      await databaseService.saveFarmer(farmer);
    }
    print('Added ${sampleFarmers.length} sample farmers');
  } catch (e) {
    print('Error adding sample farmers: $e');
  }
}