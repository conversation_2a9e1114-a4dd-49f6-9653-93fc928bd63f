import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import 'dart:io';
import '../theme/app_theme.dart';
import '../home_screen.dart';
import '../report_screen.dart';
import '../schedule_screen.dart';
import '../screens/settings_screen.dart';
import '../screens/osm_map_screen.dart';
import '../screens/farmers_screen.dart';
import '../screens/gate_registration_screen.dart';
import '../screens/edit_gate_screen.dart';

import '../services/database_service.dart';
import '../widgets/osm_map_widget.dart';
import 'package:firebase_database/firebase_database.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({Key? key}) : super(key: key);

  @override
  State<DashboardScreen> createState() => DashboardScreenState();
}

class DashboardScreenState extends State<DashboardScreen> {
  int _selectedIndex = 0;
  late DatabaseService _databaseService;

  final List<Widget> _screens = [
    DashboardContent(),
    ReportScreen(),
    ScheduleScreen(),
    FarmersScreen(),
    SettingsScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _databaseService = Provider.of<DatabaseService>(context, listen: false);

    // Start real-time schedule listeners to ensure schedules are executed
    _databaseService.startRealTimeScheduleListeners();

    print('Started real-time schedule listeners in DashboardScreen');
  }

  @override
  void dispose() {
    // Stop the listeners when the dashboard is disposed
    _databaseService.stopRealTimeScheduleListeners();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('FarmFlow'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          // Only show the add gate button on the dashboard tab
          if (_selectedIndex == 0) ...[
            IconButton(
              icon: const Icon(Icons.bug_report),
              tooltip: 'Debug Database',
              onPressed: () {
                _showDatabaseDebug();
              },
            ),
            IconButton(
              icon: const Icon(Icons.add_circle),
              tooltip: 'Register New Gate',
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const GateRegistrationScreen(),
                  ),
                ).then((result) {
                  // Refresh the dashboard if a gate was added
                  if (result == true) {
                    setState(() {
                      // This will rebuild the dashboard
                    });
                  }
                });
              },
            ),
          ],
        ],
      ),
      body: _screens[_selectedIndex],
      floatingActionButton: _selectedIndex == 0 ? FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const GateRegistrationScreen(),
            ),
          ).then((result) {
            // Refresh the dashboard if a gate was added
            if (result == true) {
              setState(() {
                // This will rebuild the dashboard
              });
            }
          });
        },
        icon: const Icon(Icons.add),
        label: const Text('Add Gate'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ) : null,
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        backgroundColor: AppTheme.backgroundColor,
        selectedItemColor: AppTheme.primaryColor,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.history),
            label: 'Reports',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.schedule),
            label: 'Schedule',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people),
            label: 'Farmers',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ],
      ),
    );
  }

  // Debug method to show database state
  Future<void> _showDatabaseDebug() async {
    try {
      final databaseService = Provider.of<DatabaseService>(context, listen: false);

      // Show loading dialog first
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: const Text('Checking Firebase Status...'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Testing Firebase connection and permissions'),
              ],
            ),
          ),
        );
      }

      // Check Firebase status
      final firebaseStatus = await databaseService.checkFirebaseStatus();
      final gates = await databaseService.getAllGates().catchError((e) {
        print('Error getting gates in debug: $e');
        return <Map<String, dynamic>>[];
      });

      if (mounted) {
        Navigator.pop(context); // Close loading dialog

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Firebase Debug Info'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('🔧 Firebase Status:', style: TextStyle(fontWeight: FontWeight.bold)),
                  Text('• Configured: ${firebaseStatus['configured'] ? '✅' : '❌'}'),
                  Text('• Connected: ${firebaseStatus['connected'] ? '✅' : '❌'}'),
                  Text('• Can Read: ${firebaseStatus['canRead'] ? '✅' : '❌'}'),
                  Text('• Can Write: ${firebaseStatus['canWrite'] ? '✅' : '❌'}'),
                  if (firebaseStatus['error'] != null)
                    Text('• Error: ${firebaseStatus['error']}', style: TextStyle(color: Colors.red)),
                  SizedBox(height: 16),
                  Text('📊 Gates Found: ${gates.length}', style: TextStyle(fontWeight: FontWeight.bold)),
                  if (gates.isNotEmpty) ...[
                    SizedBox(height: 8),
                    ...gates.map((g) => Text('• ${g['id']}: ${g['gateName'] ?? g['name'] ?? 'Unnamed'} (${g['gateStatus'] ?? 'unknown'})')),
                  ] else
                    Text('No gates found in database'),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
              if (firebaseStatus['canWrite'] == true)
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _createTestGates();
                  },
                  child: const Text('Create Test Gates'),
                ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog if open
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error in debug check: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Create real gates in Firebase database for testing
  Future<void> _createTestGates() async {
    try {
      final databaseService = Provider.of<DatabaseService>(context, listen: false);

      // Create multiple test gates with realistic data
      final testGates = [
        {
          'gateId': 'G001',
          'data': {
            'gateName': 'Main Gate',
            'phoneNumber': '+639974861230',
            'location': 'North Field Entrance',
            'description': 'Primary irrigation gate for north field',
            'gateStatus': 'closed',
            'isRegistered': true,
            'registrationMethod': 'manual',
            'registeredAt': DateTime.now().toIso8601String(),
            'lastUpdated': DateTime.now().toIso8601String(),
            'latitude': 8.217449,
            'longitude': 125.034214,
          }
        },
        {
          'gateId': 'G002',
          'data': {
            'gateName': 'Secondary Gate',
            'phoneNumber': '+639550694166',
            'location': 'South Field Entrance',
            'description': 'Secondary irrigation gate for south field',
            'gateStatus': 'open',
            'isRegistered': true,
            'registrationMethod': 'manual',
            'registeredAt': DateTime.now().toIso8601String(),
            'lastUpdated': DateTime.now().toIso8601String(),
            'latitude': 8.217403,
            'longitude': 125.034144,
          }
        },
        {
          'gateId': 'G003',
          'data': {
            'gateName': 'East Gate',
            'phoneNumber': '+639851881949',
            'location': 'East Field Section',
            'description': 'Irrigation gate for east field section',
            'gateStatus': 'closed',
            'isRegistered': true,
            'registrationMethod': 'manual',
            'registeredAt': DateTime.now().toIso8601String(),
            'lastUpdated': DateTime.now().toIso8601String(),
            'latitude': 8.217543,
            'longitude': 125.034147,
          }
        },
      ];

      for (var gate in testGates) {
        await databaseService.registerGate(gate['gateId'] as String, gate['data'] as Map<String, dynamic>);
        print('✅ Created gate ${gate['gateId']} in Firebase database');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✅ Created ${testGates.length} test gates in Firebase database!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      print('❌ Error creating test gates: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error creating test gates: $e'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }
}

class DashboardContent extends StatefulWidget {
  DashboardContent({Key? key}) : super(key: key);

  @override
  State<DashboardContent> createState() => _DashboardContentState();
}

class _DashboardContentState extends State<DashboardContent> {
  List<Map<String, dynamic>> _gates = [];
  bool _isLoading = true;
  String? _error;
  StreamSubscription<DatabaseEvent>? _gatesSubscription;

  @override
  void initState() {
    super.initState();
    _loadGates();
    _setupGatesListener();
  }

  @override
  void dispose() {
    _gatesSubscription?.cancel();
    super.dispose();
  }

  // Set up real-time listener for gates
  void _setupGatesListener() {
    try {
      final databaseService = Provider.of<DatabaseService>(context, listen: false);
      _gatesSubscription = databaseService.listenToAllGates().listen((event) {
        print('Gates data changed, reloading...');
        _loadGates();
      });
    } catch (e) {
      print('Error setting up gates listener: $e');
    }
  }

  Future<void> _loadGates() async {
    final databaseService = Provider.of<DatabaseService>(context, listen: false);

    try {
      print('🔄 Dashboard: Starting to load gates...');
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Skip connectivity check on mobile and go directly to loading gates
      // Mobile platforms have longer timeouts built into getAllGates()
      print('📊 Dashboard: Loading gates from Firebase database (mobile-optimized)...');
      final gates = await databaseService.getAllGates();

      print('✅ Dashboard: Successfully loaded ${gates.length} gates');
      for (var gate in gates) {
        print('📊 Gate: ${gate['id']} - ${gate['gateName'] ?? gate['name'] ?? 'Unnamed'} - Status: ${gate['gateStatus'] ?? 'unknown'}');
      }

      if (mounted) {
        setState(() {
          _gates = gates;
          _isLoading = false;
        });
        print('🎯 Dashboard: UI updated with ${gates.length} gates from Firebase database');
      } else {
        print('⚠️ Dashboard: Widget not mounted, skipping UI update');
      }
    } catch (e) {
      print('❌ Dashboard: Error loading gates: $e');
      print('❌ Dashboard: Error type: ${e.runtimeType}');

      // On mobile, if Firebase completely fails, provide sample data as fallback
      if (Platform.isAndroid || Platform.isIOS) {
        print('📱 Dashboard: Mobile platform detected, attempting fallback to sample data...');
        try {
          final sampleGates = await databaseService.getAllGatesWithFallback();
          if (mounted) {
            setState(() {
              _isLoading = false;
              _gates = sampleGates;
              _error = null;
            });
            print('✅ Dashboard: Mobile fallback successful, loaded ${sampleGates.length} sample gates');

            // Show mobile fallback notification
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    Icon(Icons.phone_android, color: Colors.white),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text('Mobile Mode: Using sample data. Firebase connection issue detected.'),
                    ),
                  ],
                ),
                backgroundColor: Colors.orange,
                duration: Duration(seconds: 5),
                action: SnackBarAction(
                  label: 'Retry',
                  textColor: Colors.white,
                  onPressed: () => _loadGates(),
                ),
              ),
            );
          }
          return; // Exit early, don't set error state
        } catch (fallbackError) {
          print('❌ Dashboard: Mobile fallback also failed: $fallbackError');
        }
      }

      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
        print('🔴 Dashboard: Error state set in UI');
      } else {
        print('⚠️ Dashboard: Widget not mounted, skipping error UI update');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get screen size for responsive design
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;

    return RefreshIndicator(
      onRefresh: _loadGates,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(), // Allows pull-to-refresh even when content doesn't scroll
        child: Padding(
          padding: EdgeInsets.all(isSmallScreen ? 8.0 : 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
            Text(
              'Gate Status Overview',
              style: TextStyle(
                fontSize: isSmallScreen ? 20 : 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: isSmallScreen ? 12 : 20),
            // OpenStreetMap - Mobile optimized and directly embedded
            Container(
              height: MediaQuery.of(context).size.height * (isSmallScreen ? 0.35 : 0.4), // Adjust height based on screen size
              margin: EdgeInsets.symmetric(horizontal: 0), // Full width on mobile
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(40),
                    blurRadius: 8,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: OSMMapWidget(
                  height: MediaQuery.of(context).size.height * (isSmallScreen ? 0.35 : 0.4),
                  // Enable basic interactions directly in the dashboard
                  isFullScreen: false,
                ),
              ),
            ),

            // Map title and info
            Padding(
              padding: EdgeInsets.fromLTRB(isSmallScreen ? 8 : 16, 16, isSmallScreen ? 8 : 16, 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Gate Locations',
                    style: TextStyle(
                      fontSize: isSmallScreen ? 16 : 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const OSMMapScreen(),
                        ),
                      );
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 12, vertical: isSmallScreen ? 4 : 6),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withAlpha(30),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.fullscreen,
                            size: isSmallScreen ? 14 : 16,
                            color: Theme.of(context).primaryColor,
                          ),
                          SizedBox(width: 4),
                          Text(
                            'Full Map',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 10 : 12,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: isSmallScreen ? 12 : 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Registered Gates',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 16 : 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    if (!_isLoading)
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${_gates.length} gates',
                          style: TextStyle(
                            fontSize: 12,
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    SizedBox(width: 8),
                    IconButton(
                      icon: Icon(Icons.refresh, size: 20),
                      onPressed: _isLoading ? null : _loadGates,
                      tooltip: 'Refresh gates',
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: isSmallScreen ? 6 : 10),
            // Help text for gate interactions
            if (_gates.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Text(
                  'Tap to view details • Long press for options',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                    fontStyle: FontStyle.italic,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            // Display gates based on loading state
            if (_isLoading)
              SizedBox(
                height: 200,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('Loading gates...'),
                    ],
                  ),
                ),
              )
            else if (_error != null)
              SizedBox(
                height: 350,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.cloud_off, size: 48, color: Colors.red),
                      SizedBox(height: 16),
                      Text(
                        'Cannot connect to Firebase',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          'Unable to fetch gates from database. Check your internet connection and Firebase configuration.',
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                        ),
                      ),
                      SizedBox(height: 8),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          'Error: $_error',
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                        ),
                      ),
                      SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ElevatedButton(
                            onPressed: _loadGates,
                            child: Text('Retry'),
                          ),
                          SizedBox(width: 16),
                          ElevatedButton(
                            onPressed: () {
                              // Show debug dialog from DashboardContent
                              final dashboardScreen = context.findAncestorStateOfType<DashboardScreenState>();
                              dashboardScreen?._showDatabaseDebug();
                            },
                            style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                            child: Text('Debug'),
                          ),
                        ],
                      ),
                      SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          final dashboardScreen = context.findAncestorStateOfType<DashboardScreenState>();
                          dashboardScreen?._createTestGates();
                        },
                        style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                        child: Text('Create Test Gates'),
                      ),
                    ],
                  ),
                ),
              )
            else if (_gates.isEmpty)
              SizedBox(
                height: 300,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.sensors_off, size: 48, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'No gates found in database',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Firebase connection successful but no gates registered yet',
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ElevatedButton(
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => GateRegistrationScreen(),
                                ),
                              );
                            },
                            child: Text('Add Gate'),
                          ),
                          SizedBox(width: 16),
                          ElevatedButton(
                            onPressed: () {
                              final dashboardScreen = context.findAncestorStateOfType<DashboardScreenState>();
                              dashboardScreen?._createTestGates();
                            },
                            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                            child: Text('Create Test Gates'),
                          ),
                        ],
                      ),
                      SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          // Show debug dialog from DashboardContent
                          final dashboardScreen = context.findAncestorStateOfType<DashboardScreenState>();
                          dashboardScreen?._showDatabaseDebug();
                        },
                        style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                        child: Text('Debug Database'),
                      ),
                    ],
                  ),
                ),
              )
            else
              // Use a SizedBox with a fixed height instead of Container
              SizedBox(
                height: MediaQuery.of(context).size.height * (isSmallScreen ? 0.25 : 0.3), // Adjust height based on screen size
                child: GridView.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: isSmallScreen ? 8 : 16,
                    mainAxisSpacing: isSmallScreen ? 8 : 16,
                    childAspectRatio: isSmallScreen ? 0.85 : 1.0, // Increased aspect ratio to give more height
                  ),
                  itemCount: _gates.length,
                  itemBuilder: (context, index) {
                    final gate = _gates[index];
                    return _buildGateCard(
                      context,
                      gate['gateName'] ?? gate['name'] ?? 'Gate ${gate['id']}',
                      gate['id'] ?? 'G${index + 1}',
                      gate,
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGateCard(BuildContext context, String gateName, String gateId, [Map<String, dynamic>? gateData]) {
    final databaseService = Provider.of<DatabaseService>(context, listen: false);

    // Gate data will be used in the card content

    // Get screen size for responsive design
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;

    return GestureDetector(
      onTap: () {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => HomeScreen(selectedGateId: gateId),
          ),
        );
      },
      onLongPress: () {
        _showGateOptionsMenu(context, gateId, gateName, gateData);
      },
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(isSmallScreen ? 8 : 12),
        ),
        child: Stack(
          children: [
            // Main card content
            Positioned.fill(
              child: Padding(
                padding: EdgeInsets.all(isSmallScreen ? 8.0 : 16.0),
                child: gateData != null
                    ? _buildGateCardContent(context, gateId, gateName, gateData, isSmallScreen)
                    : StreamBuilder<DatabaseEvent>(
                        stream: databaseService.listenToGateData(gateId),
                        builder: (context, snapshot) {
                          // Default status is 'unknown' if we can't get data
                          String gateStatus = 'unknown';
                          Map<String, dynamic>? data;

                          // If we have data, extract the gate status
                          if (snapshot.hasData && snapshot.data!.snapshot.exists) {
                            data = Map<String, dynamic>.from(snapshot.data!.snapshot.value as Map);
                            gateStatus = data['gateStatus'] ?? 'unknown';
                          }

                          // Determine icon color based on gate status
                          Color iconColor = gateStatus == 'open'
                              ? Colors.green
                              : (gateStatus == 'closed' || gateStatus == 'close'
                                  ? Colors.red
                                  : AppTheme.primaryColor);

                          return _buildGateCardContent(context, gateId, gateName, data ?? {}, isSmallScreen, gateStatus: gateStatus, iconColor: iconColor);
                        },
                      ),
              ),
            ),
            // Options indicator
            Positioned(
              top: 4,
              right: 4,
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Icon(
                  Icons.more_vert,
                  size: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationStatusIndicator(Map<String, dynamic> data, bool isSmallScreen) {
    // Get location status from gate data
    final locationStatus = data['locationStatus'] ?? 'no_location_yet';

    // Determine status text and color
    String statusText;
    Color statusColor;
    IconData statusIcon;

    switch (locationStatus) {
      case 'located':
        statusText = 'Located';
        statusColor = Colors.green;
        statusIcon = Icons.location_on;
      case 'gps_signal_lost':
        statusText = 'GPS Lost';
        statusColor = Colors.orange;
        statusIcon = Icons.location_off;
      case 'no_location_yet':
      default:
        statusText = 'No Location';
        statusColor = Colors.grey;
        statusIcon = Icons.location_disabled;
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          statusIcon,
          size: isSmallScreen ? 10 : 12,
          color: statusColor,
        ),
        SizedBox(width: 2),
        Text(
          statusText,
          style: TextStyle(
            fontSize: isSmallScreen ? 10 : 12,
            color: statusColor,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  // Show gate options menu
  void _showGateOptionsMenu(BuildContext context, String gateId, String gateName, Map<String, dynamic>? gateData) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Gate Options: $gateName',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              ListTile(
                leading: const Icon(Icons.visibility, color: Colors.blue),
                title: const Text('View Details'),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => HomeScreen(selectedGateId: gateId),
                    ),
                  );
                },
              ),
              ListTile(
                leading: const Icon(Icons.edit, color: Colors.orange),
                title: const Text('Edit Gate'),
                onTap: () {
                  Navigator.pop(context);
                  if (gateData != null) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => EditGateScreen(gateData: gateData),
                      ),
                    ).then((result) {
                      if (result == true || result == 'deleted') {
                        // Refresh the gates list
                        _loadGates();
                      }
                    });
                  }
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('Delete Gate'),
                onTap: () {
                  Navigator.pop(context);
                  _showDeleteConfirmation(context, gateId, gateName);
                },
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Show delete confirmation dialog
  void _showDeleteConfirmation(BuildContext context, String gateId, String gateName) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Gate'),
          content: Text('Are you sure you want to delete $gateName ($gateId)?\n\nThis action cannot be undone.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.pop(context);
                await _deleteGate(gateId, gateName);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );
  }

  // Delete gate method
  Future<void> _deleteGate(String gateId, String gateName) async {
    try {
      final databaseService = Provider.of<DatabaseService>(context, listen: false);
      await databaseService.deleteGate(gateId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$gateName deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );

        // Refresh the gates list
        _loadGates();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting gate: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildGateCardContent(BuildContext context, String gateId, String gateName, Map<String, dynamic> data, bool isSmallScreen, {String? gateStatus, Color? iconColor}) {
    // Get gate status from data if not provided
    final status = gateStatus ?? data['gateStatus'] ?? data['status'] ?? 'unknown';

    // Determine icon color if not provided
    final color = iconColor ?? (status == 'open'
        ? Colors.green
        : (status == 'closed' || status == 'close'
            ? Colors.red
            : AppTheme.primaryColor));

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Gate icon - make it flexible to prevent overflow
        Flexible(
          flex: 2,
          child: Icon(
            Icons.door_front_door,
            size: isSmallScreen ? 28 : 36, // Reduced size to prevent overflow
            color: color,
          ),
        ),
        SizedBox(height: isSmallScreen ? 2 : 4), // Reduced spacing

        // Gate name - make it flexible
        Flexible(
          flex: 1,
          child: Text(
            gateName,
            style: TextStyle(
              fontSize: isSmallScreen ? 12 : 14, // Reduced font size
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),

        SizedBox(height: isSmallScreen ? 1 : 2), // Reduced spacing

        // Gate ID - make it flexible
        Flexible(
          flex: 1,
          child: Text(
            'ID: $gateId',
            style: TextStyle(
              fontSize: isSmallScreen ? 10 : 12, // Reduced font size
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
          ),
        ),

        SizedBox(height: isSmallScreen ? 1 : 2), // Reduced spacing

        // Location status indicator - make it flexible
        Flexible(
          flex: 1,
          child: _buildLocationStatusIndicator(data, isSmallScreen),
        ),

        // Gate status badge - make it flexible and only show if there's space
        if (status != 'unknown')
          Flexible(
            flex: 1,
            child: Padding(
              padding: EdgeInsets.only(top: isSmallScreen ? 2.0 : 4.0), // Reduced padding
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 4 : 6, // Reduced padding
                  vertical: isSmallScreen ? 1 : 2,
                ),
                decoration: BoxDecoration(
                  color: status == 'open'
                      ? Colors.green.shade50
                      : Colors.red.shade50,
                  borderRadius: BorderRadius.circular(isSmallScreen ? 6 : 8), // Reduced radius
                ),
                child: Text(
                  status.toUpperCase(),
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.bold,
                    fontSize: isSmallScreen ? 8 : 10, // Reduced font size
                  ),
                ),
              ),
            ),
          ),

        // Show phone number if available
        if (data['phoneNumber'] != null)
          Padding(
            padding: EdgeInsets.only(top: isSmallScreen ? 2.0 : 4.0),
            child: Text(
              data['phoneNumber'].toString(),
              style: TextStyle(
                fontSize: isSmallScreen ? 8 : 10,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
      ],
    );
  }
}